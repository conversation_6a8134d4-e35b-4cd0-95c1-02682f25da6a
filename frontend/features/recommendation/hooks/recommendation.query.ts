import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { RecommendationQueryParams } from '../models/recommendation.type';
import { recommendationApi } from '../services/recommendation.api';

const recommendationQueryKeys = createQueryKeys(CacheKey.Recommendations, {
  list: (params: WithPaginationDefaults<RecommendationQueryParams>) => ({
    queryKey: [params],
    queryFn: () => recommendationApi.list(handleSkipOfPagination(params)),
  }),

  overal: {
    queryKey: null,
    queryFn: recommendationApi.overal,
  },
});

const useList = (params: WithPaginationDefaults<RecommendationQueryParams>) =>
  useQuery({
    ...recommendationQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });

const useOveral = () => useQuery(recommendationQueryKeys.overal);

export const recommendationQuery = {
  query: {
    useList,
    useOveral,
  },
};
