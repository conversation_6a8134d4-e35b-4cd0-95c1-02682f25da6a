'use client';

import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  RESOURCE_REGION_OPTIONS,
  RESOURCE_STATUS_OPTIONS,
} from '@/features/resource/data/resource.constants';
import { useFormFilter } from '@/hooks/use-form-filter';
import { RESOURCE_TYPE_OPTIONS } from '@/lib/constants/resource-types';
import { omit } from 'lodash';

import { RecommendationQueryParams } from '../models/recommendation.type';

type Props = {
  defaultValues: RecommendationQueryParams;
};

export function RecommendationFilters({ defaultValues }: Props) {
  const { form, onSubmit } = useFormFilter<RecommendationQueryParams>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'search',
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        <FormField
          control={control}
          name="search"
          render={({ field }) => (
            <FormItem className="grow md:max-w-lg">
              <FormControl>
                <Input
                  placeholder={`Search by title, description...`}
                  className={'w-full'}
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <FormField
            control={control}
            name="resource_id"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    mode="multiple"
                    options={RESOURCE_TYPE_OPTIONS}
                    {...field}
                    name="resource name"
                    PopoverContentProps={{
                      className: 'md:w-[340px]',
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="recommendation_type"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    options={RESOURCE_STATUS_OPTIONS}
                    mode="multiple"
                    {...field}
                    name="recommendation type"
                    PopoverContentProps={{
                      className: 'md:w-[340px]',
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    mode="multiple"
                    options={RESOURCE_REGION_OPTIONS}
                    PopoverContentProps={{
                      className: 'md:w-[240px]',
                    }}
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* <Button variant="default" className="gap-2">
          <XIcon className="size-4" />
          Clear Filters
        </Button> */}
      </form>
    </Form>
  );
}
