'use client';

import DashboardCard from '@/components/dashboard-card';
import {
  CollectionsIcon,
  DocumentsIcon,
  SavingsIcon,
  TotalResourcesIcon,
} from '@/components/dashboard-icons';
import { PageSkeleton } from '@/components/ui/common/page';

import { recommendationQuery } from '../hooks/recommendation.query';

export function RecommendationOveral() {
  const { data, isLoading } = recommendationQuery.query.useOveral();

  if (data) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DashboardCard
          title="Total Resources Scanned"
          value={data.total_resource_scanned}
          icon={<TotalResourcesIcon />}
        />

        <DashboardCard
          title="Well Optimized"
          value={data.total_well_optimized}
          icon={<CollectionsIcon />}
        />

        <DashboardCard
          title="Optimization Opportunities"
          value={data.total_optimization_opportunities}
          icon={<DocumentsIcon />}
        />

        <DashboardCard
          title="Estimated saving amount"
          value={data.total_estimated_saving_amount}
          icon={<SavingsIcon />}
        />
      </div>
    );
  }

  if (isLoading) {
    return <PageSkeleton />;
  }

  return null;
}
