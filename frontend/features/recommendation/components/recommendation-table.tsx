'use client';

import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import { NewDataTable } from '@/components/ui/table/new-data-table';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { recommendationQuery } from '../hooks/recommendation.query';
import { RecommendationQueryParams } from '../models/recommendation.type';
import { recommendationColumns } from './recommendation.columns';

type RecommendationTableProps = {
  searchParams: WithPaginationDefaults<RecommendationQueryParams>;
};

export function RecommendationTable({
  searchParams,
}: RecommendationTableProps) {
  const { data, isLoading, isRefetching } =
    recommendationQuery.query.useList(searchParams);

  return (
    <If
      condition={data}
      fallback={
        <If condition={isLoading}>
          <PageSkeleton />
        </If>
      }
    >
      {(data) => (
        <NewDataTable
          data={data.data}
          columns={recommendationColumns}
          pageSize={10}
          pageIndex={Number(searchParams.page ?? 1) - 1}
          pageCount={Math.ceil(data.count / 10)}
          isRefetching={isRefetching}
          itemCount={data.count}
        />
      )}
    </If>
  );
}
