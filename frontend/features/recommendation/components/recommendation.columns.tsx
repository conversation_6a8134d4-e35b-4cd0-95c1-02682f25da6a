import Link from 'next/link';

import { ActionCellWrapper } from '@/components/ui/common/action-cell-wrapper';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { ResourceTypeBadge } from '@/components/ui/resource-type-badge';
import { SeverityBadge } from '@/components/ui/severity-badge';
import { StatusBadge } from '@/components/ui/status-badge';
import pathsConfig from '@/config/paths.config';
import { RecommendationPublic } from '@/openapi-ts/types';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon } from 'lucide-react';

export const recommendationColumns: ColumnDef<RecommendationPublic>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    size: 200,
    minSize: 150,
  },
  {
    accessorKey: 'description',
    header: 'Description',
    size: 400,
    minSize: 350,
    cell: ({ row }) => {
      const value = row.original.description;
      return (
        <div className="max-w-none break-words whitespace-normal">{value}</div>
      );
    },
  },
  {
    accessorKey: 'type',
    header: 'Recommendation Type',
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.type;
      return value?.replace(/_/g, ' ')?.toUpperCase();
    },
  },
  {
    accessorKey: 'resource.name',
    header: 'Resource Name',
    size: 180,
    minSize: 150,
  },
  {
    accessorKey: 'resource.type',
    header: 'Resource Type',
    size: 140,
    minSize: 120,
    cell: ({ row }) => {
      const { type } = row.original.resource;

      return type ? <ResourceTypeBadge resourceType={type} /> : null;
    },
  },
  {
    accessorKey: 'potential_savings',
    header: () => <div className="text-left">Monthly Savings ($)</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.potential_savings;
      return (
        <div className="text-left">
          {value !== null && value !== undefined ? `$${value}` : '-'}
        </div>
      );
    },
  },
  {
    accessorKey: 'effort',
    header: () => <div className="text-left">Effort</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.effort;
      return (
        <div className="justify-left flex">
          {value ? (
            <SeverityBadge severity={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'risk',
    header: () => <div className="text-left">Risk</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.risk;
      return (
        <div className="justify-left flex">
          {value ? (
            <SeverityBadge severity={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.status;
      return (
        <div className="justify-left flex">
          {value ? (
            <StatusBadge status={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    id: 'actions',
    size: 80,
    minSize: 60,
    cell: ({ row }) => (
      <RecommendationCellAction recommendation={row.original} />
    ),
  },
];

const RecommendationCellAction = ({
  recommendation,
}: {
  recommendation: RecommendationPublic;
}) => {
  return (
    <ActionCellWrapper>
      <DropdownMenuItem>
        <Link
          href={pathsConfig.app.recommendationDetail(recommendation.id)}
          prefetch
          className="flex h-full w-full items-center gap-2"
        >
          <EditIcon className="size-4" /> Details
        </Link>
      </DropdownMenuItem>
    </ActionCellWrapper>
  );
};
