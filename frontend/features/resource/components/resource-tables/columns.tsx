'use client';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { ResourceTypeBadge } from '@/components/ui/resource-type-badge';
import { StatusBadge } from '@/components/ui/status-badge';
import pathsConfig from '@/config/paths.config';
import { formatUSD } from '@/lib/currency';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { ResourcePublic } from '@/openapi-ts/types';
import { ColumnDef } from '@tanstack/react-table';

import { CellAction } from './cell-action';

export const resourceColumns: ColumnDef<ResourcePublic>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
    size: 280,
    cell: ({ row }) => {
      const { id, name } = row.original;
      return (
        <Button
          asChild
          variant="link"
          className="h-auto px-0 whitespace-nowrap"
        >
          <Link href={pathsConfig.app.resourceDetail(id)} prefetch>
            {name}
          </Link>
        </Button>
      );
    },
    meta: {
      className: 'whitespace-nowrap',
    },
  },
  {
    accessorKey: 'region',
    header: 'Region',
    size: 140,
    meta: {
      className: 'whitespace-nowrap',
    },
  },
  {
    accessorKey: 'type',
    header: 'Resource Type',
    cell: ({ row }) => {
      const value = row.original.type;
      return value ? <ResourceTypeBadge resourceType={value} /> : null;
    },
    size: 160,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const value = row.original.status;
      if (!value) {
        throw new Error('Status is required');
      }

      return (
        <div className="flex justify-center">
          <StatusBadge status={value} className="p-2 opacity-80" />
        </div>
      );
    },
    size: 110,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'total_recommendation',
    header: 'Total Recommendations',
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'total_potential_saving',
    header: 'Total Potential Saving',
    cell: ({ row }) => {
      const value = row.original.total_potential_saving;
      if (typeof value !== 'number') {
        return 'N/A';
      }

      return <p>{formatUSD(value)}</p>;
    },
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated',
    cell: renderCellToFullDateTime,
    size: 230,
    meta: {
      className: 'text-center whitespace-nowrap',
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />,
    size: 60,
  },
];
