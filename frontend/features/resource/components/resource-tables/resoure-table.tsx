'use client';

import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import { NewDataTable } from '@/components/ui/table/new-data-table';
import { resourceQuery } from '@/features/resource/hooks/resource.query';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { ResourceQueryParams } from '../../models/resource.type';
import { resourceColumns } from './columns';

export function ResourceTable({
  searchParams,
}: {
  searchParams: WithPaginationDefaults<ResourceQueryParams>;
}) {
  const { data, isLoading, isRefetching } =
    resourceQuery.query.useList(searchParams);

  return (
    <If
      condition={data}
      fallback={
        <If condition={isLoading}>
          <PageSkeleton />
        </If>
      }
    >
      {(data) => (
        <NewDataTable
          data={data.data}
          columns={resourceColumns}
          pageSize={10}
          pageIndex={Number(searchParams.page ?? 1) - 1}
          pageCount={Math.ceil(data.count / 10)}
          isRefetching={isRefetching}
          itemCount={data.count}
        />
      )}
    </If>
  );
}
