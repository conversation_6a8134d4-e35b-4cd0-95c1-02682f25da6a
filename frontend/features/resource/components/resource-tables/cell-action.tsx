'use client';

import Link from 'next/link';

import { ActionCellWrapper } from '@/components/ui/common/action-cell-wrapper';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import pathsConfig from '@/config/paths.config';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { ResourcePublic } from '@/openapi-ts/types';
import { flow } from 'lodash';
import { EditIcon, ViewIcon } from 'lucide-react';

type CellActionProps = {
  data: ResourcePublic;
};

export const CellAction = ({ data }: CellActionProps) => {
  // Using lodash flow for functional composition:
  // 1. Takes resourceId and transforms it into RecommendationQueryParams object
  // 2. Transforms the params object into a URL string with query parameters
  // 3. Finally applies the composed function to data.id
  const allRecommendationsHref = flow(
    (resourceId: string): RecommendationQueryParams => ({
      resource_id: [resourceId],
    }),
    (params) =>
      `${pathsConfig.app.recommendations}?${new URLSearchParams(params as URLSearchParams).toString()}`,
  )(data.id);

  return (
    <ActionCellWrapper>
      <DropdownMenuItem>
        <Link
          href={allRecommendationsHref}
          prefetch
          className="flex h-full w-full items-center gap-2"
        >
          <ViewIcon className="size-4" /> All Recommendations
        </Link>
      </DropdownMenuItem>

      <DropdownMenuItem>
        <Link
          href={pathsConfig.app.resourceDetail(data.id)}
          prefetch
          className="flex h-full w-full items-center gap-2"
        >
          <EditIcon className="size-4" /> Details
        </Link>
      </DropdownMenuItem>
    </ActionCellWrapper>
  );
};
