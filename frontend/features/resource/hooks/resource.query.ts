import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { ResourceQueryParams } from '../models/resource.type';
import { resourceApi } from '../services/resource.api';

const resourceQueryKeys = createQueryKeys(CacheKey.Resources, {
  list: (params: WithPaginationDefaults<ResourceQueryParams>) => ({
    queryKey: [params],
    queryFn: () => resourceApi.list(handleSkipOfPagination(params)),
  }),
});

const useList = (params: WithPaginationDefaults<ResourceQueryParams>) => {
  return useQuery({
    ...resourceQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

export const resourceQuery = {
  query: {
    useList,
  },
};
