import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { TaskQueryParams } from '../models/task.type';

export const taskApi = {
  list: (query: TaskQueryParams) =>
    fetchData(
      api.GET('/api/v1/tasks/', {
        params: {
          query,
        },
      }),
    ),

  detail: (id: string) => ({
    updateEnable: (body: { enable: boolean }) =>
      api.PATCH('/api/v1/tasks/{task_id}/enable', {
        params: {
          path: {
            task_id: id,
          },
          query: body,
        },
      }),

    delete: () =>
      api.DELETE('/api/v1/tasks/{task_id}', {
        params: {
          path: { task_id: id },
        },
      }),
  }),
};
