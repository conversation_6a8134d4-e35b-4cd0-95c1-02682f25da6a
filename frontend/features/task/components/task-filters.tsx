'use client';

import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { useFormFilter } from '@/hooks/use-form-filter';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';

import { TASK_STATUS_CONFIG } from '../config/task-status.config';
import { TaskQueryParams } from '../models/task.type';

type FormFilter = WithPaginationDefaults<TaskQueryParams>;

type Props = {
  defaultValues: TaskQueryParams;
};

export const TaskFilters = ({ defaultValues }: Props) => {
  const { form, onSubmit } = useFormFilter<FormFilter>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        {/* <FormField
      control={control}
      name="name"
      render={({ field }) => (
        <FormItem className="grow md:max-w-lg">
          <FormControl>
            <Input
              placeholder={`Search resource by name...`}
              className={'w-full'}
              {...field}
            />
          </FormControl>
        </FormItem>
      )}
    /> */}
        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <FormField
            control={control}
            name="execution_status"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    options={TASK_STATUS_CONFIG.LIST}
                    {...field}
                    name="status"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};
