'use client';

import { PropsWithChildren } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { TaskExecutionStatus } from '@/openapi-ts/gens';
import { TaskResponse } from '@/openapi-ts/types';

import { taskQuery } from '../hooks/task.query';

export const TaskEnableSwitch = ({ task }: { task: TaskResponse }) => {
  const { mutate: updateEnable, isPending } =
    taskQuery.mutation.useUpdateEnable(task.id);

  const onCheckedChange = (enable: boolean) => {
    updateEnable({ enable });
  };

  const showDialog =
    task.enable && task.execution_status === TaskExecutionStatus.running;

  return (
    <UpdateWrapper isPending={isPending}>
      <EnableTaskAlertDialog
        showDialog={showDialog}
        onCheckedChange={onCheckedChange}
      >
        <Switch
          checked={task.enable}
          onCheckedChange={showDialog ? undefined : onCheckedChange}
          className={cn({
            'pointer-events-none opacity-50': isPending,
          })}
        />
      </EnableTaskAlertDialog>
    </UpdateWrapper>
  );
};

const EnableTaskAlertDialog = ({
  children,
  showDialog,
  onCheckedChange,
}: PropsWithChildren<{
  showDialog: boolean;
  onCheckedChange: (enable: boolean) => void;
}>) => {
  if (!showDialog) return children;

  return (
    <AlertDialog>
      <AlertDialogTrigger>{children}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Disable Task</AlertDialogTitle>
          <AlertDialogDescription>
            Task is currently running. Disabling will prevent future executions.
            Are you sure you want to disable this task?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={() => onCheckedChange(false)}>
            Disable
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
