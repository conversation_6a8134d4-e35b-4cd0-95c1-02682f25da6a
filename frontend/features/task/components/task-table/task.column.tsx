import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ActionCellWrapper } from '@/components/ui/common/action-cell-wrapper';
import { If } from '@/components/ui/common/if';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import {
  formatToMinutesAndSeconds,
  renderCellToFullDateTime,
} from '@/lib/date-utils';
import { TaskResponse } from '@/openapi-ts/types';
import { ColumnDef } from '@tanstack/react-table';
import { meanBy } from 'lodash';
import { TrashIcon, ViewIcon } from 'lucide-react';
import pluralize from 'pluralize';

import { TASK_STATUS_CONFIG } from '../../config/task-status.config';
import { TaskEnableSwitch } from '../task-enable-switch';
import { ConfirmDeleteTask } from './confirm-delete-task';

export const taskColumns: ColumnDef<TaskResponse>[] = [
  {
    accessorKey: 'title',
    header: 'Task Name',
    size: 350,
    cell: ({ row }) => {
      const { title, description } = row.original;

      return (
        <div className="space-y-1">
          <Button variant="link" className="p-0 font-semibold">
            <Link href={pathsConfig.app.taskDetail(row.original.id)} prefetch>
              {title}
            </Link>
          </Button>
          <If condition={description}>
            {(description) => (
              <WithTooltip
                tooltip={description}
                contentClassName="max-w-[320px]"
              >
                <div className="text-muted-foreground line-clamp-2 max-w-[320px] text-xs">
                  {description}
                </div>
              </WithTooltip>
            )}
          </If>
        </div>
      );
    },
  },
  {
    accessorKey: 'execution_status',
    header: 'Status',
    size: 120,
    cell: ({ row }) => {
      const { execution_status } = row.original;

      if (!execution_status) {
        return <Badge variant={'warning'}>Pending</Badge>;
      }

      const { label, variant } = TASK_STATUS_CONFIG.OBJECT[execution_status];

      return <Badge variant={variant}>{label}</Badge>;
    },
  },
  {
    accessorKey: 'enable',
    header: 'Schedule Enabled',
    cell: ({ row }) => <TaskEnableSwitch task={row.original} />,
    meta: {
      className: 'text-center w-[120px]',
    },
  },
  {
    accessorKey: 'task_history',
    header: 'Duration',
    size: 120,
    cell: ({ row }) => {
      const { task_history } = row.original;

      if (!task_history || task_history.length === 0)
        return <span className="text-muted-foreground">N/A</span>;

      const runTimeAvg = meanBy(task_history, (v) => v.run_time ?? 0);

      return (
        <div className="space-y-1">
          <p>{formatToMinutesAndSeconds(runTimeAvg)}</p>
          <p className="text-muted-foreground text-xs">
            {pluralize('run', task_history.length, true)}
          </p>
        </div>
      );

      //   if (!taskHistory || taskHistory.length === 0) {
      //     return (
      //       <div className="px-3 text-center">
      //         <div className="text-sm font-medium italic text-muted-foreground">
      //           No data
      //         </div>
      //       </div>
      //     );
      //   }

      //   // Calculate average runtime from completed executions
      //   const completedRuns = taskHistory.filter(
      //     (run) =>
      //       run.start_time &&
      //       run.end_time &&
      //       (getStatusText(run.status) === 'succeeded' ||
      //         getStatusText(run.status) === 'failed' ||
      //         getStatusText(run.status) === 'cancelled'),
      //   );

      //   if (completedRuns.length === 0) {
      //     return (
      //       <div className="px-3 text-center">
      //         <div className="text-sm font-medium italic text-muted-foreground">
      //           No completed runs
      //         </div>
      //       </div>
      //     );
      //   }

      //   // Calculate average duration in milliseconds
      //   const totalDuration = completedRuns.reduce((sum, run) => {
      //     const start = new Date(run.start_time!);
      //     const end = new Date(run.end_time!);
      //     return sum + (end.getTime() - start.getTime());
      //   }, 0);

      //   const averageDurationMs = totalDuration / completedRuns.length;

      //   // Format average duration
      //   const formatAverageDuration = (durationMs: number): string => {
      //     if (durationMs < 1000) return `${Math.round(durationMs)}ms`;
      //     if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`;
      //     if (durationMs < 3600000) return `${Math.round(durationMs / 60000)}m`;
      //     return `${Math.round(durationMs / 3600000)}h`;
      //   };

      //   const averageDuration = formatAverageDuration(averageDurationMs);

      //   return (
      //     <div className="px-3 text-center">
      //       <div className="font-mono text-sm font-semibold text-foreground">
      //         {averageDuration}
      //       </div>
      //       <div className="text-xs text-muted-foreground">
      //         {completedRuns.length} run{completedRuns.length !== 1 ? 's' : ''}
      //       </div>
      //     </div>
      //   );
    },
    meta: {
      className: 'text-center',
    },
  },

  {
    accessorKey: 'last_run',
    header: 'Last Run',
    size: 150,
    cell: renderCellToFullDateTime,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'next_run',
    header: 'Next Run',
    size: 150,
    cell: renderCellToFullDateTime,
    meta: {
      className: 'text-center',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    size: 170,
    cell: ({ row }) => <TaskCellAction task={row.original} />,
    meta: {
      className: 'text-right',
    },
  },
];

const TaskCellAction = ({ task }: { task: TaskResponse }) => {
  return (
    <ActionCellWrapper>
      <DropdownMenuItem>
        <Link
          href={pathsConfig.app.taskDetail(task.id)}
          prefetch
          className="flex h-full w-full items-center gap-2"
        >
          <ViewIcon className="size-4" /> Details
        </Link>
      </DropdownMenuItem>
      <ConfirmDeleteTask taskId={task.id}>
        <DropdownMenuItem
          onSelect={(e) => e.preventDefault()}
          className="text-destructive/80 hover:!text-destructive flex h-full w-full items-center gap-2"
        >
          <TrashIcon className="size-4" /> Delete
        </DropdownMenuItem>
      </ConfirmDeleteTask>
    </ActionCellWrapper>
  );
};
