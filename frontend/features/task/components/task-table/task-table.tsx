'use client';

import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import { NewDataTable } from '@/components/ui/table/new-data-table';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { taskQuery } from '../../hooks/task.query';
import { TaskQueryParams } from '../../models/task.type';
import { taskColumns } from './task.column';

type TaskTableProps = {
  searchParams: WithPaginationDefaults<TaskQueryParams>;
};

export function TaskTable({ searchParams }: TaskTableProps) {
  const { data, isLoading, isRefetching } =
    taskQuery.query.useList(searchParams);

  return (
    <If
      condition={data}
      fallback={
        <If condition={isLoading}>
          <PageSkeleton />
        </If>
      }
    >
      {(data) => (
        <NewDataTable
          data={data.data ?? []}
          columns={taskColumns}
          pageSize={10}
          pageIndex={Number(searchParams.page ?? 1) - 1}
          pageCount={Math.ceil(data.total / 10)}
          isRefetching={isRefetching}
          itemCount={data.total}
        />
      )}
    </If>
  );
}
