import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { taskQuery } from '../../hooks/task.query';

type Props = PropsWithChildren<{
  taskId: string;
}>;

export const ConfirmDeleteTask = ({ taskId, children }: Props) => {
  const { mutate: deleteTask, isPending } =
    taskQuery.mutation.useDelete(taskId);

  const onConfirm = (toggle: () => void) => {
    deleteTask(undefined, {
      onSuccess: toggle,
    });
  };
  return (
    <DeleteConfirmAlert
      title="Delete Task"
      loading={isPending}
      onConfirm={onConfirm}
    >
      {children}
    </DeleteConfirmAlert>
  );
};
