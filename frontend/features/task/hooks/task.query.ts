import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { TaskQueryParams } from '../models/task.type';
import { taskApi } from '../services/task.api';

const taskQueryKeys = createQueryKeys(CacheKey.Items, {
  list: (params: WithPaginationDefaults<TaskQueryParams>) => ({
    queryKey: [params],
    queryFn: () => taskApi.list(handleSkipOfPagination(params)),
  }),
});

const useList = (params: WithPaginationDefaults<TaskQueryParams>) => {
  return useQuery({
    ...taskQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useUpdateEnable = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: taskApi.detail(id).updateEnable,
    onSuccess: () => {
      toast.success('Task enabled');
      queryClient.invalidateQueries({
        queryKey: taskQueryKeys.list._def,
      });
    },
  });
};

const useDelete = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: taskApi.detail(id).delete,
    onSuccess: () => {
      toast.success('Task deleted');
      queryClient.invalidateQueries({
        queryKey: taskQueryKeys.list._def,
      });
    },
  });
};

export const taskQuery = {
  query: {
    useList,
  },
  mutation: {
    useUpdateEnable,
    useDelete,
  },
};
