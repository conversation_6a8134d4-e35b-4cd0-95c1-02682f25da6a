import { BadgeProps } from '@/components/ui/badge';
import { TaskExecutionStatus } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const TASK_STATUS_CONFIG = createUtilityConfig({
  [TaskExecutionStatus.succeeded]: {
    label: 'Succeeded',
    // icon: CheckCircleIcon,
    variant: 'success',
  },
  [TaskExecutionStatus.failed]: {
    label: 'Failed',
    // icon: XCircleIcon,
    variant: 'destructive',
  },
  [TaskExecutionStatus.running]: {
    label: 'Running',
    // icon: RefreshCcwIcon,
    variant: 'warning',
  },
  [TaskExecutionStatus.cancelled]: {
    label: 'Cancelled',
    // icon: StopCircleIcon,
    variant: 'cancelled',
  },
  [TaskExecutionStatus.required_approval]: {
    label: 'Required Approval',
    // icon: HandIcon,
    variant: 'info',
  },
} satisfies Record<
  TaskExecutionStatus,
  {
    label: string;
    // icon: FC<{ className: string }>;
    variant: BadgeProps['variant'];
  }
>);
