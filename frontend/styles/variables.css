/* ==========================================================================
      CSS Custom Properties (Variables)
      ========================================================================== */
@layer base {
  :root {
    /* Brand Colors */
    --brand-teal: #0fb3b3;
    --brand-blue: #1093c6;
    --brand-green: #06ef6b;

    /* Base Colors */
    --background: var(--color-white);
    --foreground: #161d1d;
    --muted: #d2e4e4;
    --muted-foreground: #3b4444;
    --popover: var(--color-white);
    --popover-foreground: #161d1d;
    --card: var(--color-white);
    --card-foreground: #161d1d;
    --border: #edf2f2;
    --input: #edf2f2;

    /* Interactive Colors */
    --primary: var(--brand-teal);
    --primary-foreground: var(--color-white);
    --secondary: #e8eded;
    --secondary-foreground: #485b5b;
    --accent: var(--brand-teal);
    --accent-foreground: var(--color-white);

    /* Status Colors - Improved for better accessibility and modern design */
    --success: var(--brand-green);
    --success-foreground: var(--color-white);
    --info: var(--brand-blue);
    --info-foreground: var(--color-white);
    --warning: #f4b740;
    --warning-foreground: var(--color-white);
    /* Updated destructive colors - warmer, more accessible red */
    --destructive: var(--color-red-500);
    --destructive-foreground: var(--color-white);

    /* UI Elements */
    --ring: var(--brand-teal);
    --radius: 0.5rem;

    /* Sidebar Theme */
    --sidebar-background: var(--color-white);
    --sidebar-foreground: #3f4646;
    --sidebar-primary: var(--brand-teal);
    --sidebar-primary-foreground: var(--color-white);
    --sidebar-accent: #d2e4e4;
    --sidebar-accent-foreground: #161d1d;
    --sidebar-border: #e5ebeb;
    --sidebar-ring: var(--brand-teal);

    /* Animation */
    --transition-duration: 0.2s;
    --transition-ease: cubic-bezier(0.2, 0, 0.2, 1);
  }

  /* Dark Theme Overrides */
  .dark {
    --background: #101218;
    --foreground: #e2e4e9;
    --muted: #161a22;
    --muted-foreground: #98a1b3;
    --popover: #12151c;
    --popover-foreground: #e2e4e9;
    --card: #161a22;
    --card-foreground: #e2e4e9;
    --border: var(--color-neutral-800);
    --input: #21242c;

    --primary: #0fbbc7;
    --primary-foreground: var(--color-neutral-900);

    --secondary: #181c25;
    --secondary-foreground: #d3d7de;

    --accent: #21242c;
    --accent-foreground: #e8eaee;

    --success: #34b262;
    --success-foreground: #101218;

    --info: #25a0d0;
    --info-foreground: #101218;

    --warning: #dc9518;
    --warning-foreground: #101218;
    /* Improved dark mode destructive color - softer, more eye-friendly */
    --destructive-foreground: #101218;

    --ring: #21242c;
    --sidebar-background: #0e1016;
    --sidebar-foreground: #e2e4e9;
    /* --sidebar-muted: #171a21;
    --sidebar-muted-foreground: #9ca3af; */
    --sidebar-accent: #161a22;
    --sidebar-accent-foreground: #e2e4e9;
    --sidebar-border: #21242c;
  }
}
