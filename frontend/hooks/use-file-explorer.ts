import { useState, useCallback } from 'react';
import { ConsoleProxyService } from '@/client/sdk.gen';
import type { FileNode as BackendFileNode } from '@/client/types.gen';
import { ApiError } from '@/client/core/ApiError';

interface ErrorResponse {
  detail: string;
}

export interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  content?: string;
}

// Convert backend file node to our internal format
const convertFileNode = (node: BackendFileNode): FileNode => {
  return {
    id: node.path,
    name: node.name,
    path: node.path,
    type: node.type,
    children: node.children?.map(convertFileNode)
  };
};

export const useFileExplorer = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getFiles = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await ConsoleProxyService.getFiles();
      return response.files.map(convertFileNode);
    } catch (err) {
      if (err instanceof ApiError) {
        const errorBody = err.body as ErrorResponse;
        setError(errorBody?.detail || err.message);
      } else {
        setError('Failed to fetch files');
      }
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getFileContent = useCallback(async (path: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await ConsoleProxyService.getFileContent({ path });
      return response.content;
    } catch (err) {
      if (err instanceof ApiError) {
        const errorBody = err.body as ErrorResponse;
        setError(errorBody?.detail || err.message);
        throw err; // Re-throw to let the component handle the error
      } else {
        const error = new Error('Failed to fetch file content');
        setError(error.message);
        throw error;
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    getFiles,
    getFileContent,
    isLoading,
    error,
  };
};
