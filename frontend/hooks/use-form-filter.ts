import { useEffect } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { isArray, pickBy } from 'lodash';
import { DefaultValues, useForm } from 'react-hook-form';
import { useDebounceCallback } from 'usehooks-ts';

import { useCallbackRef } from './use-callback-ref';

export const useFormFilter = <T extends object>({
  defaultValues,
  debounceField,
}: {
  defaultValues: DefaultValues<T>;
  debounceField?: keyof T;
}) => {
  const form = useForm<T>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues,
  });

  const router = useRouter();
  const pathName = usePathname();

  const onSubmit = useCallbackRef((values: T) => {
    const params = new URLSearchParams();
    //   pickBy(values) as Record<string, string>,

    const filtered = pickBy(values, (val) => {
      if (val === undefined) return false;
      return isArray(val) ? val.length > 0 : !!val;
    }) as Record<string, string>;

    for (const [key, value] of Object.entries(filtered)) {
      if (Array.isArray(value)) {
        value.forEach((v) => params.append(key, v));
      } else {
        params.append(key, value);
      }
    }

    const url = `${pathName}?${params.toString()}`;

    router.push(url, {
      scroll: false,
      //@ts-expect-error: shallow is not a valid option for router.push
      shallow: true,
    });
  });

  const debouncedOnSubmit = useDebounceCallback(onSubmit, 300);

  useEffect(() => {
    // Only watch keyword field with debounce
    const subscription = form.watch((value, { name }) => {
      if (name === debounceField) {
        debouncedOnSubmit(value as T);
      } else {
        // Submit immediately for industry and status
        onSubmit(value as T);
      }
    });

    return subscription.unsubscribe;
  }, [debouncedOnSubmit, onSubmit, form.watch]);

  return {
    form,
    onSubmit,
  };
};
