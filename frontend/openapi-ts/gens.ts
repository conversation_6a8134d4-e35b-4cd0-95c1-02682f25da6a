/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/v1/login/access-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login Access Token
         * @description OAuth2 compatible token login, get an access token for future requests
         */
        post: operations["login-login_access_token"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/login/test-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Token
         * @description Test access token
         */
        post: operations["login-test_token"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/password-recovery/{email}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password
         * @description Password Recovery
         */
        post: operations["login-recover_password"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reset-password/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset Password
         * @description Reset password
         */
        post: operations["login-reset_password"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/password-recovery-html-content/{email}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Recover Password Html Content
         * @description HTML Content for Password Recovery
         */
        post: operations["login-recover_password_html_content"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Users
         * @description Retrieve users based on workspace relationship.
         *     Only returns users that belong to the current user's active workspace.
         */
        get: operations["users-read_users"];
        put?: never;
        /**
         * Create User
         * @description Create new user.
         */
        post: operations["users-create_user"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/users/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User Me
         * @description Get current user.
         */
        get: operations["users-read_user_me"];
        put?: never;
        post?: never;
        /**
         * Delete User Me
         * @description Delete own user.
         */
        delete: operations["users-delete_user_me"];
        options?: never;
        head?: never;
        /**
         * Update User Me
         * @description Update own user.
         */
        patch: operations["users-update_user_me"];
        trace?: never;
    };
    "/api/v1/users/me/password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Password Me
         * @description Update own password.
         */
        patch: operations["users-update_password_me"];
        trace?: never;
    };
    "/api/v1/users/{user_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read User By Id
         * @description Get a specific user by id.
         */
        get: operations["users-read_user_by_id"];
        put?: never;
        post?: never;
        /**
         * Delete User
         * @description Delete a user.
         */
        delete: operations["users-delete_user"];
        options?: never;
        head?: never;
        /**
         * Update User
         * @description Update a user.
         */
        patch: operations["users-update_user"];
        trace?: never;
    };
    "/api/v1/users/switch-workspace/{workspace_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Switch Workspace
         * @description Allow user to get new token for a different workspace.
         */
        get: operations["users-switch_workspace"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/test-email/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Test Email
         * @description Test emails.
         */
        post: operations["utils-test_email"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/health-check/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Health Check */
        get: operations["utils-health_check"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/publish/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Publish Message */
        post: operations["utils-publish_message"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/utils/enqueue/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Enqueue Message */
        post: operations["utils-enqueue_message"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/items/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Items
         * @description Retrieve items.
         */
        get: operations["items-read_items"];
        put?: never;
        /**
         * Create Item
         * @description Create new item.
         */
        post: operations["items-create_item"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/items/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Item
         * @description Get item by ID.
         */
        get: operations["items-read_item"];
        /**
         * Update Item
         * @description Update an item.
         */
        put: operations["items-update_item"];
        post?: never;
        /**
         * Delete Item
         * @description Delete an item.
         */
        delete: operations["items-delete_item"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/aws-accounts/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Aws Accounts
         * @description Retrieve AWS accounts.
         */
        get: operations["aws-accounts-read_aws_accounts"];
        put?: never;
        /**
         * Create Aws Account
         * @description Create new AWS account.
         */
        post: operations["aws-accounts-create_aws_account"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/aws-accounts/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Aws Account
         * @description Get AWS account by ID.
         */
        get: operations["aws-accounts-read_aws_account"];
        /**
         * Update Aws Account
         * @description Update an AWS account.
         */
        put: operations["aws-accounts-update_aws_account"];
        post?: never;
        /**
         * Delete Aws Account
         * @description Delete an AWS account.
         */
        delete: operations["aws-accounts-delete_aws_account"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/resources/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Resources
         * @description Retrieve resources.
         */
        get: operations["resources-read_resources"];
        put?: never;
        /**
         * Create Resource
         * @description Create new resource.
         */
        post: operations["resources-create_resource"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/resources/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Resource
         * @description Get resource by ID.
         */
        get: operations["resources-read_resource"];
        /**
         * Update Resource
         * @description Update a resource.
         */
        put: operations["resources-update_resource"];
        post?: never;
        /**
         * Delete Resource
         * @description Delete a resource.
         */
        delete: operations["resources-delete_resource"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/metrics/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Metrics
         * @description Retrieve metrics.
         */
        get: operations["metrics-read_metrics"];
        put?: never;
        /**
         * Create Metric
         * @description Create new metric.
         */
        post: operations["metrics-create_metric"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/metrics/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Metric
         * @description Get metric by ID.
         */
        get: operations["metrics-read_metric"];
        /**
         * Update Metric
         * @description Update a metric.
         */
        put: operations["metrics-update_metric"];
        post?: never;
        /**
         * Delete Metric
         * @description Delete a metric.
         */
        delete: operations["metrics-delete_metric"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/overal": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Recomendation Overal
         * @description Get overal recommendation statistics.
         */
        get: operations["recommendations-get_recomendation_overal"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Recommendations
         * @description Retrieve recommendations.
         */
        get: operations["recommendations-read_recommendations"];
        put?: never;
        /**
         * Create Recommendation
         * @description Create new recommendation.
         */
        post: operations["recommendations-create_recommendation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Recommendation
         * @description Get recommendation by ID.
         */
        get: operations["recommendations-read_recommendation"];
        /**
         * Update Recommendation
         * @description Update a recommendation.
         */
        put: operations["recommendations-update_recommendation"];
        post?: never;
        /**
         * Delete Recommendation
         * @description Delete a recommendation.
         */
        delete: operations["recommendations-delete_recommendation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/recommendations/{id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Recommendation Status
         * @description Update the status of a recommendation.
         */
        put: operations["recommendations-update_recommendation_status"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workflows
         * @description Retrieve workflows.
         */
        get: operations["workflows-read_workflows"];
        put?: never;
        /**
         * Create Workflow
         * @description Create new workflow.
         */
        post: operations["workflows-create_workflow"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workflow
         * @description Get workflow by ID.
         */
        get: operations["workflows-read_workflow"];
        /**
         * Update Workflow
         * @description Update a workflow.
         */
        put: operations["workflows-update_workflow"];
        post?: never;
        /**
         * Delete Workflow
         * @description Delete a workflow.
         */
        delete: operations["workflows-delete_workflow"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/template": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Workflow From Template
         * @description Create new workflow from YAML template.
         */
        post: operations["workflows-create_workflow_from_template"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/nodes/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workflow Nodes
         * @description Retrieve workflow nodes.
         */
        get: operations["workflows-read_workflow_nodes"];
        put?: never;
        /**
         * Create Workflow Node
         * @description Create new workflow node.
         */
        post: operations["workflows-create_workflow_node"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/nodes/{node_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workflow Node
         * @description Get workflow node by ID.
         */
        get: operations["workflows-read_workflow_node"];
        /**
         * Update Workflow Node
         * @description Update a workflow node.
         */
        put: operations["workflows-update_workflow_node"];
        post?: never;
        /**
         * Delete Workflow Node
         * @description Delete a workflow node.
         */
        delete: operations["workflows-delete_workflow_node"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/nodes/{node_id}/run": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Run Workflow Node
         * @description Run a specific workflow node.
         */
        post: operations["workflows-run_workflow_node"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/run": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Run Workflow
         * @description Run an entire workflow.
         */
        post: operations["workflows-run_workflow"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workspaces/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workspaces
         * @description Retrieve workspaces - both owned and invited (non-deleted only).
         */
        get: operations["workspaces-read_workspaces"];
        put?: never;
        /**
         * Create Workspace
         * @description Create new workspace. Only users who already own workspaces or superusers can create new ones.
         */
        post: operations["workspaces-create_workspace"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workspaces/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Workspace
         * @description Get Workspace by ID. Accessible by workspace owner and invited users.
         */
        get: operations["workspaces-read_workspace"];
        /**
         * Update Workspace
         * @description Update a workspace. Only workspace owners can perform this action.
         */
        put: operations["workspaces-update_workspace"];
        post?: never;
        /**
         * Delete Workspace
         * @description Delete a workspace. Only workspace owners can perform this action.
         */
        delete: operations["workspaces-delete_workspace"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tools/run": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Script Execution
         * @description Script execution to the bash environment.
         */
        post: operations["tools-script_execution"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/console/files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Files
         * @description Get files for the current user's workspace by proxying to executor
         */
        get: operations["console-proxy-get_files"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/console/files/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get File Content
         * @description Get file content from executor for display in the file explorer
         */
        get: operations["console-proxy-get_file_content"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Agents
         * @description Retrieve Agents for the current workspace.
         */
        get: operations["agents-read_agents"];
        put?: never;
        /**
         * Create Agent
         * @description Create new Agent.
         */
        post: operations["agents-create_agent"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Read Agent
         * @description Get Agent by ID.
         */
        get: operations["agents-read_agent"];
        /** Update Agent */
        put: operations["agents-update_agent"];
        post?: never;
        /** Delete Agent */
        delete: operations["agents-delete_agent"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/init-default/{workspace_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Init Default Agents
         * @description Initialize default agents for a workspace.
         */
        post: operations["agents-init_default_agents"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tasks
         * @description List tasks with filters.
         */
        get: operations["tasks-list_tasks"];
        put?: never;
        /**
         * Create Task
         * @description Create new task.
         */
        post: operations["tasks-create_task"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task
         * @description Get task by ID.
         */
        get: operations["tasks-get_task"];
        /**
         * Update Task
         * @description Update task.
         */
        put: operations["tasks-update_task"];
        post?: never;
        /**
         * Delete Task
         * @description Delete task.
         */
        delete: operations["tasks-delete_task"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/enable": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Task Enable
         * @description Update task enable.
         */
        patch: operations["tasks-update_task_enable"];
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/stop": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Stop Task Execution
         * @description Stop an executing task.
         */
        post: operations["tasks-stop_task_execution"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Progress
         * @description Get task execution progress by conversation id.
         */
        get: operations["tasks-get_task_progress"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/task-history/{task_history_id}/continue": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Continue Interrupted Task
         * @description Continue an interrupted autonomous agent task.
         */
        post: operations["tasks-continue_interrupted_task"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Conversations
         * @description Get list of conversations with filtering and pagination.
         *
         *     Args:
         *         current_user: The authenticated user
         *         session: Database session
         *         agent_id: Optional agent ID to filter by
         *         resource_id: Optional resource ID to filter by
         *         model_provider: Model provider to use (defaults to 'bedrock')
         *         skip: Number of records to skip for pagination
         *         limit: Maximum number of records to return (1-100)
         *
         *     Returns:
         *         ConversationsPublic: Paginated list of conversations
         *
         *     Raises:
         *         HTTPException: If conversation not found or other error occurs
         */
        get: operations["autonomous-agents-get_conversations"];
        put?: never;
        /**
         * Create Conversation
         * @description Create a new conversation.
         */
        post: operations["autonomous-agents-create_conversation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/messages/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Messages History
         * @description Get message history for a conversation.
         */
        get: operations["autonomous-agents-get_messages_history"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/chat/{conversation_id}/stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chat Stream
         * @description Stream chat responses from the agent.
         *
         *     Args:
         *         conversation_id: ID of the conversation
         *         message: User message with content and resume flag
         *         session: Database session
         *
         *     Returns:
         *         StreamingResponse: Server-sent events stream of agent responses
         *
         *     Raises:
         *         HTTPException: If conversation not found (404) or no previous message found (404)
         */
        post: operations["autonomous-agents-chat_stream"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations/{conversation_id}/name": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** Rename Conversation */
        put: operations["autonomous-agents-rename_conversation"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/autonomous-agents/conversations/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Delete Conversation
         * @description Delete a conversation and its associated LangGraph thread data.
         */
        delete: operations["autonomous-agents-delete_conversation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/memory/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /** Update Memory */
        put: operations["memory-update_memory"];
        /**
         * Get Memory
         * @description Get all memories for given agent roles. If agent roles are not provided, get all memories for all agent roles.
         */
        post: operations["memory-get_memory"];
        /** Delete Memory */
        delete: operations["memory-delete_memory"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sample-data/resources/{resource_type}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Sample Resources
         * @description Generate sample resources and metrics for testing.
         *
         *     Args:
         *         resource_type: Type of resource (EC2, RDS, etc.)
         *         resource_count: Number of resources to generate
         *         metrics_per_resource: Number of metric points per resource
         *         days_back: Number of days to generate metrics for
         */
        post: operations["sample-data-create_sample_resources"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sample-data/metrics/{resource_type}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Sample Metrics
         * @description Create sample metrics for specified resource type.
         *
         *     Parameters:
         *     - resource_type: Type of resource (EC2, RDS, etc.)
         *     - num_points: Number of data points to generate per metric
         *     - days_back: Number of days to generate data for
         */
        post: operations["sample-data-create_sample_metrics"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/sample-data/recommendations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Create Sample Recommendations */
        post: operations["sample-data-create_sample_recommendations"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Google Login
         * @description Initiate Google OAuth login flow
         */
        get: operations["google-google_login"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google/callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Google Callback
         * @description Handle Google OAuth callback and login/create user
         */
        get: operations["google-google_callback"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Create Usage */
        post: operations["quotas-create_usage"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/message-statistics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Messages Statistics
         * @description Get message usage statistics for a workspace.
         *
         *     Args:
         *         start_date: Optional start date for filtering
         *         end_date: Optional end date for filtering
         *
         *     Returns:
         *         Message statistics including:
         *         - Total messages and month-over-month change
         *         - Average response time and month-over-month change
         *         - Success rate and month-over-month change
         *         - Average tokens per message (input/output)
         *         - Daily message volume (30-day trend)
         *         - Token distribution by message length
         */
        get: operations["quotas-get_messages_statistics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Usage Quota
         * @description Get usage quota for a specific workspace.
         *
         *     Args:
         *         user_id: ID of the user
         *
         *     Returns:
         *         Usage quota details
         */
        get: operations["quotas-get_usage_quota"];
        put?: never;
        /** Create Usage Quota */
        post: operations["quotas-create_usage_quota"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/reset": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reset User Quota
         * @description Reset usage quota for a user.
         *
         *     Args:
         *         user_id: ID of the user
         *
         *     Returns:
         *         Reset usage quota details
         */
        post: operations["quotas-reset_user_quota"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/statistics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Usage Statistics
         * @description Get token usage statistics for a workspace.
         *
         *     Args:
         *         user_id: ID of the user
         *         start_date: Optional start date for filtering
         *         end_date: Optional end date for filtering
         *
         *     Returns:
         *         Usage statistics
         */
        get: operations["quotas-get_usage_statistics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/quotas/{user_id}/quota-info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Quota Info
         * @description Get quota information for a user.
         *
         *     Args:
         *         user_id: ID of the user
         */
        get: operations["quotas-get_quota_info"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/saving-summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Savings Summary
         * @description Get total potential savings for the workspace with comparison between two periods.
         */
        get: operations["reports-get_savings_summary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/resource-saving": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Savings By Resource
         * @description Get savings data grouped by date for RDS and EC2 resources.
         */
        get: operations["reports-get_savings_by_resource"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/top-potential-savings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Top Potential Savings
         * @description Get top N recommendations with highest potential savings that are in PENDING status
         *     for the current workspace within the specified date range.
         */
        get: operations["reports-get_top_potential_savings"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/service-savings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Savings By Service
         * @description Get savings data grouped by service type for pie chart visualization.
         */
        get: operations["reports-get_savings_by_service"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/reports/{conversation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Report By Conversation */
        get: operations["reports-get_report_by_conversation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/generate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate
         * @description Generate the task template based on user's input
         */
        post: operations["task_templates-generate"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Templates
         * @description List task templates with optional category and service filters.
         */
        get: operations["task_templates-list_templates"];
        put?: never;
        /**
         * Create Template
         * @description Create new task template.
         */
        post: operations["task_templates-create_template"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/task_templates/{template_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Template
         * @description Get template by ID.
         */
        get: operations["task_templates-get_template"];
        /**
         * Update Template
         * @description Update template.
         */
        put: operations["task_templates-update_template"];
        post?: never;
        /**
         * Delete Template
         * @description Delete template.
         */
        delete: operations["task_templates-delete_template"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base_runtime/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search knowledge base
         * @description Search the knowledge base with various modes and filters
         */
        post: operations["knowledge_base_runtime-search"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base_runtime/summarize": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate summary from knowledge base
         * @description Search and summarize content from knowledge base
         */
        post: operations["knowledge_base_runtime-summarize"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/files/create-url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Create Upload Url */
        post: operations["files-create_upload_url"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/files/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Check Upload Status */
        post: operations["files-check_upload_status"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Kbs
         * @description Get all knowledge bases for the current user
         */
        get: operations["knowledge_base-get_kbs"];
        put?: never;
        /** Create Kb */
        post: operations["knowledge_base-create_kb"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/available-users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Users
         * @description Get a list of users available for sharing knowledge bases within the current workspace.
         *     Returns users that are in the same workspace as the current user.
         */
        get: operations["knowledge_base-get_available_users"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/point-usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Point Usage
         * @description Get user's point usage across all knowledge bases
         */
        get: operations["knowledge_base-get_point_usage"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Kb By Id
         * @description Get a specific knowledge base by ID
         */
        get: operations["knowledge_base-get_kb_by_id"];
        /** Update Kb */
        put: operations["knowledge_base-update_kb"];
        post?: never;
        /** Delete Kb */
        delete: operations["knowledge_base-delete_kb"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Presigned Urls
         * @description Generate presigned URLs for file uploads.
         *
         *     This endpoint generates presigned URLs that clients can use to upload files
         *     directly to S3, bypassing the backend for better performance and scalability.
         */
        post: operations["knowledge_base-generate_presigned_urls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/confirm-uploads": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Confirm File Uploads
         * @description Confirm file uploads and start ingestion process.
         *
         *     This endpoint should be called after files have been successfully uploaded
         *     using the presigned URLs to start the document ingestion process.
         */
        post: operations["knowledge_base-confirm_file_uploads"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Documents
         * @description List documents in a knowledge base.
         *
         *     User must have access to the knowledge base (owner for personal knowledge bases,
         *     workspace member for workspace knowledge bases).
         */
        get: operations["knowledge_base-list_documents"];
        put?: never;
        /** Upload Urls */
        post: operations["knowledge_base-upload_urls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents/content": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get Document Content */
        get: operations["knowledge_base-get_document_content"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /** Delete Document */
        delete: operations["knowledge_base-delete_document"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/knowledge_base/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task Status
         * @description Get the status of an asynchronous task.
         *
         *     This endpoint returns the current status and progress of a Celery task,
         *     such as document ingestion.
         */
        get: operations["knowledge_base-get_task_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connectors/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Connectors
         * @description List knowledge bases.
         */
        get: operations["connectors-list_connectors"];
        put?: never;
        /**
         * Create Connector
         * @description Create new knowledge base.
         */
        post: operations["connectors-create_connector"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/connectors/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Connector
         * @description Get knowledge base by ID.
         */
        get: operations["connectors-get_connector"];
        /**
         * Update Connector
         * @description Update knowledge base.
         */
        put: operations["connectors-update_connector"];
        post?: never;
        /**
         * Delete Connector
         * @description Delete an item.
         */
        delete: operations["connectors-delete_connector"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/plans": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Plans
         * @description Get available plans
         */
        get: operations["subscriptions-get_available_plans"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get User Subscription Status */
        get: operations["subscriptions-get_user_subscription_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/workspace/{workspace_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workspace Subscription Status
         * @description Get subscription status for a workspace
         */
        get: operations["subscriptions-get_workspace_subscription_status"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/checkout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Checkout Session
         * @description Create a checkout session for subscription
         */
        post: operations["subscriptions-create_checkout_session"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/payment-methods": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Payment Methods
         * @description Get current user's payment methods
         */
        get: operations["subscriptions-get_user_payment_methods"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/invoices": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Invoices
         * @description Get current user's invoices
         */
        get: operations["subscriptions-get_user_invoices"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/enterprise-enquiry": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit Enterprise Enquiry
         * @description Submit an enterprise plan enquiry
         */
        post: operations["subscriptions-submit_enterprise_enquiry"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/plan-change": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit Plan Change Request
         * @description Submit a plan change request
         */
        post: operations["subscriptions-submit_plan_change_request"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/webhook": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Webhook
         * @description Handle webhook events from payment provider
         */
        post: operations["subscriptions-webhook"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/subscriptions/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel Subscription
         * @description Cancel subscription for the current user
         */
        post: operations["subscriptions-cancel_subscription"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/module_setting/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Module Settings
         * @description Retrieve all module settings.
         */
        get: operations["module_setting-get_module_settings"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/summary/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Alert Status Summary
         * @description Get a summary of alerts by status for the last 30 days.
         */
        get: operations["alerts-get_alert_status_summary"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Alerts
         * @description List alerts with optional filters.
         */
        get: operations["alerts-list_alerts"];
        put?: never;
        /**
         * Create Alert
         * @description Create new alert.
         */
        post: operations["alerts-create_alert"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/{alert_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Alert
         * @description Get alert by ID.
         */
        get: operations["alerts-get_alert"];
        /**
         * Update Alert
         * @description Update alert.
         */
        put: operations["alerts-update_alert"];
        post?: never;
        /**
         * Delete Alert
         * @description Delete alert.
         */
        delete: operations["alerts-delete_alert"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/alerts/{alert_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update Alert Status
         * @description Update alert status.
         */
        patch: operations["alerts-update_alert_status"];
        trace?: never;
    };
    "/api/v1/alerts/mark-all-acknowledged": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Mark All Alerts Acknowledged
         * @description Mark all alerts as acknowledged for the current workspace.
         */
        post: operations["alerts-mark_all_alerts_acknowledged"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/signup": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register
         * @description Register new user and send activation email.
         */
        post: operations["auth-register"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/activate/{token}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Activate Account
         * @description Activate a user account using the activation token.
         */
        post: operations["auth-activate_account"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/resend-activation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resend Activation
         * @description Resend activation email for unactivated accounts with reCAPTCHA v3 validation.
         */
        post: operations["auth-resend_activation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** List Notifications */
        get: operations["notifications-list_notifications"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/{notification_id}/mark-read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Mark Notification Read */
        post: operations["notifications-mark_notification_read"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/notifications/mark-all-read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Mark All Notifications Read */
        post: operations["notifications-mark_all_notifications_read"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/message-feedback/message/{message_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Message Feedback
         * @description Get feedback for a specific message.
         */
        get: operations["message-feedback-get_message_feedback"];
        /**
         * Update Message Feedback
         * @description Update feedback for a message.
         */
        put: operations["message-feedback-update_message_feedback"];
        post?: never;
        /**
         * Delete Message Feedback
         * @description Delete feedback for a message.
         */
        delete: operations["message-feedback-delete_message_feedback"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/message-feedback/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Message Feedback
         * @description Create feedback for a message.
         */
        post: operations["message-feedback-create_message_feedback"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/mcp-server/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Mcp Servers
         * @description Get all MCP servers for a workspace.
         *
         *     Returns information about all configured MCP servers for the workspace,
         *     including their connection status, available tools, and configuration details.
         */
        get: operations["mcp-server-get_mcp_servers"];
        put?: never;
        /**
         * Create Mcp Server
         * @description Create a new MCP server for a workspace.
         */
        post: operations["mcp-server-create_mcp_server"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/mcp-server/{server_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Mcp Server
         * @description Get a specific MCP server by ID.
         */
        get: operations["mcp-server-get_mcp_server"];
        /**
         * Update Mcp Server
         * @description Update an existing MCP server.
         */
        put: operations["mcp-server-update_mcp_server"];
        post?: never;
        /**
         * Delete Mcp Server
         * @description Delete an MCP server.
         */
        delete: operations["mcp-server-delete_mcp_server"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/mcp-server/{server_id}/refresh": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Refresh Mcp Server
         * @description Refresh an MCP server.
         */
        post: operations["mcp-server-refresh_mcp_server"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/builtin-connectors/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Workspace Connectors
         * @description List all built-in connectors for a workspace
         */
        get: operations["builtin-connectors-list_workspace_connectors"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/builtin-connectors/{connector_id}/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Update Connector For Workspace
         * @description Update the active status of a built-in connector for a workspace
         */
        post: operations["builtin-connectors-update_connector_for_workspace"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/builtin-connectors/{connector_id}/permission": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update Connector Permission
         * @description Update whether a tool requires human approval before execution.
         *
         *     Args:
         *         workspace_id: ID of the workspace
         *         connector_id: ID of the connector
         *         required_permission: Whether to require human approval for this tool
         *         session: Database session
         *
         *     Returns:
         *         bool: True if the permission requirement was updated successfully
         */
        put: operations["builtin-connectors-update_connector_permission"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agent-connectors": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Agent Connector
         * @description Create a new agent connector.
         */
        post: operations["agent-connectors-create_agent_connector"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agent-connectors/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Connector
         * @description Get an agent connector by agent ID.
         */
        get: operations["agent-connectors-get_agent_connector"];
        /**
         * Update Agent Connector
         * @description Update an existing agent connector.
         */
        put: operations["agent-connectors-update_agent_connector"];
        post?: never;
        /**
         * Delete Agent Connector
         * @description Delete an agent connector.
         */
        delete: operations["agent-connectors-delete_agent_connector"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agent-connectors/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Connectors By Workspace Id
         * @description Get all agent connectors by workspace ID.
         */
        get: operations["agent-connectors-get_agent_connectors_by_workspace_id"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agent-context/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Context
         * @description Get context based on agent id
         */
        get: operations["agent-context-get_agent_context"];
        /**
         * Update Agent Context
         * @description Update agent context
         */
        put: operations["agent-context-update_agent_context"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agent-context/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Contexts
         * @description Get contexts based on a list of agent ids, for each agent id, get the latest none deleted context
         */
        get: operations["agent-context-get_agent_contexts"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/share-chat/conversations/{conversation_id}/share": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Share Link
         * @description Get a share link for a conversation
         */
        get: operations["share-chat-get_share_link"];
        put?: never;
        /**
         * Create Share Link
         * @description Create a share link for a conversation
         */
        post: operations["share-chat-create_share_link"];
        /**
         * Revoke Share Link
         * @description Revoke a share link for a conversation
         */
        delete: operations["share-chat-revoke_share_link"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/share-chat/conversations/shared/{share_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Shared Conversation
         * @description Get message history for a shared conversation by share ID (no authentication required)
         */
        get: operations["share-chat-get_shared_conversation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** AWSAccountCreate */
        AWSAccountCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /** Account Id */
            account_id: string;
            /** Access Key Id */
            access_key_id: string;
            /** Secret Access Key */
            secret_access_key: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /** Cron Pattern */
            cron_pattern: string;
        };
        /** AWSAccountDetail */
        AWSAccountDetail: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Access Key Id */
            access_key_id: string;
            /** Secret Access Key */
            secret_access_key: string;
            /** Account Id */
            account_id: string;
        };
        /** AWSAccountPublic */
        AWSAccountPublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            environment: components["schemas"]["AccountEnvironement"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** AWSAccountUpdate */
        AWSAccountUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Environment */
            environment?: string | null;
            /** Account Id */
            account_id?: string | null;
            /**
             * Regions
             * @default []
             */
            regions: string[] | null;
            /**
             * Types
             * @default []
             */
            types: string[] | null;
            /** Cron Pattern */
            cron_pattern?: string | null;
            /** Access Key Id */
            access_key_id?: string | null;
            /** Secret Access Key */
            secret_access_key?: string | null;
        };
        /** AWSAccountsPublic */
        AWSAccountsPublic: {
            /** Data */
            data: components["schemas"]["AWSAccountPublic"][];
            /** Count */
            count: number;
        };
        /**
         * AccountEnvironement
         * @enum {string}
         */
        AccountEnvironement: AccountEnvironement;
        /** ActivationResponse */
        ActivationResponse: {
            /** Message */
            message: string;
            /**
             * Expires At
             * Format: date-time
             */
            expires_at: string;
        };
        /** ActivationResult */
        ActivationResult: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
            /** Redirect Url */
            redirect_url?: string | null;
            /** Welcome Message */
            welcome_message?: string | null;
        };
        /** Address */
        Address: {
            /** City */
            city?: string | null;
            /** Country */
            country?: string | null;
            /** Line1 */
            line1?: string | null;
            /** Line2 */
            line2?: string | null;
            /** Postal Code */
            postal_code?: string | null;
            /** State */
            state?: string | null;
        };
        /** AgentConnectorCreate */
        AgentConnectorCreate: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Builtin Connector Ids */
            builtin_connector_ids: string[];
            /** Mcp Servers */
            mcp_servers: string[];
        };
        /** AgentConnectorResponse */
        AgentConnectorResponse: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Builtin Connectors */
            builtin_connectors: components["schemas"]["BuiltInConnectorResponse"][];
            /** Mcp Servers */
            mcp_servers: string[];
        };
        /** AgentConnectorUpdate */
        AgentConnectorUpdate: {
            /** Builtin Connector Ids */
            builtin_connector_ids: string[];
            /** Mcp Servers */
            mcp_servers: string[];
        };
        /** AgentContextListInput */
        AgentContextListInput: {
            /** Agent Ids */
            agent_ids: string[];
        };
        /**
         * AgentContextListResponse
         * @description Response model for paginated agent context list.
         *
         *     Attributes:
         *         data: List of agent context items
         *         count: Total number of items available (before pagination)
         */
        AgentContextListResponse: {
            /** Data */
            data: components["schemas"]["AgentContextRead"][];
            /** Count */
            count: number;
        };
        /** AgentContextRead */
        AgentContextRead: {
            /** Title */
            title: string;
            /** Context */
            context: string;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** AgentContextUpdate */
        AgentContextUpdate: {
            /** Title */
            title?: string | null;
            /** Context */
            context?: string | null;
            /** Is Active */
            is_active?: boolean | null;
        };
        /** AgentCreate */
        AgentCreate: {
            /**
             * Title
             * @description The title/name of the agent
             */
            title: string;
            /**
             * Description
             * @description Detailed description of the agent's purpose
             */
            description?: string | null;
            /**
             * @description Type of the agent
             * @default conversation_agent
             */
            type: components["schemas"]["AgentType"];
            /**
             * Instructions
             * @description Custom instructions for the agent
             */
            instructions?: string | null;
        };
        /** AgentPublic */
        AgentPublic: {
            /**
             * Title
             * @description The title/name of the agent
             */
            title: string;
            /**
             * Description
             * @description Detailed description of the agent's purpose
             */
            description?: string | null;
            /**
             * @description Type of the agent
             * @default conversation_agent
             */
            type: components["schemas"]["AgentType"];
            /**
             * Instructions
             * @description Custom instructions for the agent
             */
            instructions?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Active */
            is_active?: boolean | null;
        };
        /**
         * AgentType
         * @description Defines the supported types of agents in the system.
         * @enum {string}
         */
        AgentType: AgentType;
        /** AgentTypeUsage */
        AgentTypeUsage: {
            /** Agent Type */
            agent_type: string;
            /** Total Tokens */
            total_tokens: number;
        };
        /** AgentUpdate */
        AgentUpdate: {
            /** Title */
            title?: string | null;
            /**
             * Description
             * @description Detailed description of the agent's purpose
             */
            description?: string | null;
            /**
             * @description Type of the agent
             * @default conversation_agent
             */
            type: components["schemas"]["AgentType"];
            /**
             * Instructions
             * @description Custom instructions for the agent
             */
            instructions?: string | null;
            /** Workspace Id */
            workspace_id?: string | null;
            /** Is Active */
            is_active?: boolean | null;
        };
        /** AgentsPublic */
        AgentsPublic: {
            /** Data */
            data: components["schemas"]["AgentPublic"][];
            /** Count */
            count: number;
        };
        /**
         * AlertCreate
         * @description Schema for creating a new alert
         */
        AlertCreate: {
            /**
             * Title
             * @description Alert title
             */
            title: string;
            /**
             * Description
             * @description Detailed alert description
             */
            description: string;
            /** @description Alert severity level */
            severity: components["schemas"]["AlertSeverity"];
        };
        /**
         * AlertList
         * @description Schema for list of alerts with pagination
         */
        AlertList: {
            /** Data */
            data: components["schemas"]["AlertResponse"][];
            /** Total */
            total: number;
        };
        /**
         * AlertResponse
         * @description Schema for alert response
         */
        AlertResponse: {
            /**
             * Title
             * @description Alert title
             */
            title: string;
            /**
             * Description
             * @description Detailed alert description
             */
            description: string;
            /** @description Alert severity level */
            severity: components["schemas"]["AlertSeverity"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            status: components["schemas"]["AlertStatus"];
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /**
         * AlertSeverity
         * @enum {string}
         */
        AlertSeverity: AlertSeverity;
        /**
         * AlertStatus
         * @enum {string}
         */
        AlertStatus: AlertStatus;
        /**
         * AlertStatusSummary
         * @description Summary of alerts by status for the last 30 days
         */
        AlertStatusSummary: {
            /**
             * Status Counts
             * @description Count of alerts by status
             */
            status_counts: {
                [key: string]: number;
            };
            /**
             * Total
             * @description Total number of alerts in the period
             */
            total: number;
        };
        /**
         * AlertUpdate
         * @description Schema for updating an existing alert
         */
        AlertUpdate: {
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            severity?: components["schemas"]["AlertSeverity"] | null;
            status?: components["schemas"]["AlertStatus"] | null;
        };
        /**
         * AsyncTaskStatus
         * @enum {string}
         */
        AsyncTaskStatus: AsyncTaskStatus;
        /** AvailableUser */
        AvailableUser: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Email */
            email: string;
            /** Full Name */
            full_name: string;
        };
        /** AvailableUsersCurrentWorkspace */
        AvailableUsersCurrentWorkspace: {
            /** Data */
            data: components["schemas"]["AvailableUser"][];
            /** Count */
            count: number;
        };
        /** BillingDetails */
        BillingDetails: {
            address: components["schemas"]["Address"];
            /** Email */
            email?: string | null;
            /** Name */
            name?: string | null;
            /** Phone */
            phone?: string | null;
        };
        /** Body_login-login_access_token */
        "Body_login-login_access_token": {
            /** Grant Type */
            grant_type?: string;
            /** Username */
            username: string;
            /** Password */
            password: string;
            /**
             * Scope
             * @default
             */
            scope: string;
            /** Client Id */
            client_id?: string | null;
            /** Client Secret */
            client_secret?: string | null;
            /**
             * Slackoauth
             * @default false
             */
            slackOAuth: boolean;
            /** Appid */
            appId?: string | null;
            /** Teamid */
            teamId?: string | null;
        };
        /** Body_notifications-list_notifications */
        "Body_notifications-list_notifications": {
            /** Type */
            type?: components["schemas"]["NotificationType"][] | null;
            /** Status */
            status?: components["schemas"]["NotificationStatus"][] | null;
        };
        /**
         * BuiltInConnector
         * @description Definition of built-in connectors available in the system
         */
        BuiltInConnector: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Name
             * @description Unique identifier for the connector
             */
            name: string;
            /**
             * Display Name
             * @description Human-readable name for the connector
             */
            display_name: string;
            /** Description */
            description?: string | null;
            /**
             * Default Required Permission
             * @default false
             */
            default_required_permission: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /** BuiltInConnectorResponse */
        BuiltInConnectorResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name?: string | null;
            /** Display Name */
            display_name?: string | null;
            /** Description */
            description?: string | null;
        };
        /** CardDetails */
        CardDetails: {
            /** Brand */
            brand: string;
            /** Country */
            country: string;
            /** Display Brand */
            display_brand: string;
            /** Exp Month */
            exp_month: number;
            /** Exp Year */
            exp_year: number;
            /** Last4 */
            last4: string;
        };
        /** ChartDataPoint */
        ChartDataPoint: {
            /**
             * Date
             * Format: date-time
             */
            date: string;
            /** Value */
            value: number;
        };
        /**
         * ChartType
         * @description Enum for different types of charts available in the system
         * @enum {string}
         */
        ChartType: ChartType;
        /** CheckoutSessionResponse */
        CheckoutSessionResponse: {
            /** Checkout Session Url */
            checkout_session_url: string;
        };
        /**
         * CitationMetadata
         * @description Metadata for document citations
         */
        CitationMetadata: {
            /** Ref Id */
            ref_id: number;
            /** Doc Name */
            doc_name: string;
            /** Doc Section */
            doc_section: string;
            /** Text Snippet */
            text_snippet: string;
        };
        /**
         * CloudProvider
         * @enum {string}
         */
        CloudProvider: CloudProvider;
        /**
         * ConfirmUploadsRequest
         * @description Request to confirm file uploads and start ingestion
         */
        ConfirmUploadsRequest: {
            /**
             * Uploaded Files
             * @description Information about successfully uploaded files
             */
            uploaded_files: components["schemas"]["UploadedFileInfo"][];
        };
        /** ConnectorCreate */
        ConnectorCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            type: components["schemas"]["ConnectorType"];
            /**
             * Approval
             * @default false
             */
            approval: boolean;
            /**
             * Config
             * @default {}
             */
            config: Record<string, never>;
        };
        /** ConnectorList */
        ConnectorList: {
            /** Data */
            data: components["schemas"]["BuiltInConnector"][];
            /** Total */
            total: number;
        };
        /** ConnectorResponse */
        ConnectorResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Description */
            description: string | null;
            type: components["schemas"]["ConnectorType"];
            /**
             * Approval
             * @default false
             */
            approval: boolean;
            /** Config */
            config: Record<string, never>;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Created By
             * Format: uuid
             */
            created_by: string;
            /** Updated By */
            updated_by: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at: string | null;
        };
        /**
         * ConnectorType
         * @enum {string}
         */
        ConnectorType: ConnectorType;
        /** ConnectorUpdate */
        ConnectorUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Config */
            config?: Record<string, never> | null;
            /** Approval */
            approval?: boolean | null;
        };
        /**
         * ConnectorWithStatusResponse
         * @description Response model for a connector with its active status and permission settings in a workspace.
         *
         *     Attributes:
         *         id: Unique identifier for the workspace-connector association
         *         name: Unique name of the connector
         *         display_name: Human-readable name for the connector
         *         description: Optional description of the connector
         *         is_active: Whether the connector is active in this workspace
         *         required_permission: Whether this tool requires human approval before execution
         */
        ConnectorWithStatusResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Display Name */
            display_name: string;
            /** Description */
            description?: string | null;
            /** Is Active */
            is_active: boolean;
            /**
             * Required Permission
             * @default false
             */
            required_permission: boolean;
        };
        /** ConversationCreateRequest */
        ConversationCreateRequest: {
            /**
             * Agent Id
             * Format: uuid4
             */
            agent_id: string;
            /**
             * Model Provider
             * @default bedrock
             */
            model_provider: string;
            /** Resource Id */
            resource_id?: string | null;
            /** Instructions */
            instructions?: string | null;
        };
        /** ConversationPublic */
        ConversationPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            resource?: components["schemas"]["Resource"] | null;
            /** Name */
            name: string;
            /** Model Provider */
            model_provider: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /**
         * ConversationsPublic
         * @description Response model for paginated conversations list.
         *
         *     Attributes:
         *         data: List of conversation items
         *         count: Total number of items available (before pagination)
         */
        ConversationsPublic: {
            /** Data */
            data: components["schemas"]["ConversationPublic"][];
            /** Count */
            count: number;
        };
        /** DailyMessageVolume */
        DailyMessageVolume: {
            /**
             * Date
             * Format: date-time
             */
            date: string;
            /** Message Count */
            message_count: number;
        };
        /** DailyTokenUsage */
        DailyTokenUsage: {
            /**
             * Date
             * Format: date-time
             */
            date: string;
            /** Tokens */
            total_tokens: number;
        };
        /**
         * Document
         * @description Class for storing a piece of text and associated metadata.
         *
         *     Example:
         *
         *         .. code-block:: python
         *
         *             from langchain_core.documents import Document
         *
         *             document = Document(
         *                 page_content="Hello, world!",
         *                 metadata={"source": "https://example.com"}
         *             )
         */
        Document: {
            /** Id */
            id?: string | null;
            /** Metadata */
            metadata?: Record<string, never>;
            /** Page Content */
            page_content: string;
            /**
             * Type
             * @default Document
             * @constant
             */
            type: "Document";
        };
        /** DocumentKBRead */
        DocumentKBRead: {
            /** Name */
            name: string;
            type: components["schemas"]["DocumentType"];
            /** Url */
            url?: string | null;
            /**
             * Deep Crawl
             * @default false
             */
            deep_crawl: boolean;
            /** File Name */
            file_name?: string | null;
            /** File Type */
            file_type?: string | null;
            /** Object Name */
            object_name?: string | null;
            /** @default PENDING */
            embed_status: components["schemas"]["AsyncTaskStatus"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Kb Id
             * Format: uuid
             */
            kb_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Is Deleted */
            is_deleted: boolean;
            /** Parent Id */
            parent_id?: string | null;
            /**
             * Children
             * @default []
             */
            children: components["schemas"]["DocumentKBRead"][];
        };
        /**
         * DocumentType
         * @enum {string}
         */
        DocumentType: DocumentType;
        /** DocumentsKBRead */
        DocumentsKBRead: {
            /** Data */
            data: components["schemas"]["DocumentKBRead"][];
            /** Count */
            count: number;
        };
        /**
         * EnterpriseEnquiryMessageResponse
         * @description Response model for enterprise enquiry status messages
         */
        EnterpriseEnquiryMessageResponse: {
            /** Message */
            message: string;
        };
        /** EnterpriseEnquiryRequest */
        EnterpriseEnquiryRequest: {
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Title */
            work_title: string;
            /** Work Email */
            work_email: string;
            /** Company Name */
            company_name: string;
            /** Estimated Monthly Cost */
            estimated_monthly_cost: string;
            /** Message */
            message: string;
            /**
             * Product Id
             * Format: uuid
             */
            product_id: string;
        };
        /** ErrorResponse */
        ErrorResponse: {
            /** Error */
            error: string;
            /** Details */
            details?: string | null;
        };
        /**
         * FeedbackType
         * @description Enumeration for feedback types on agent responses.
         * @enum {string}
         */
        FeedbackType: FeedbackType;
        /**
         * FileContentResponse
         * @description Response for file content requests
         */
        FileContentResponse: {
            /**
             * Content
             * @description File content
             */
            content: string;
            /**
             * Path
             * @description File path
             */
            path: string;
            /**
             * Name
             * @description File name
             */
            name: string;
        };
        /**
         * FileInfo
         * @description Information about a file to generate a presigned URL for
         */
        FileInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Content Type
             * @description File MIME type
             */
            content_type: string;
            /**
             * File Size
             * @description File size in bytes
             */
            file_size: number;
        };
        /**
         * FileListResponse
         * @description Response for directory listing
         */
        FileListResponse: {
            /**
             * Files
             * @description List of files and directories
             */
            files: components["schemas"]["FileNode"][];
            /**
             * Current Path
             * @description Current directory path
             */
            current_path: string;
        };
        /**
         * FileNode
         * @description Simplified file node matching frontend interface
         */
        FileNode: {
            /**
             * Id
             * @description Unique identifier for the file/directory
             */
            id: string;
            /**
             * Name
             * @description File or directory name
             */
            name: string;
            /**
             * Path
             * @description Full path
             */
            path: string;
            /** @description File type (file or directory) */
            type: components["schemas"]["FileType"];
            /**
             * Children
             * @description Child nodes for directories
             */
            children?: components["schemas"]["FileNode"][] | null;
            /**
             * Content
             * @description File content for files
             */
            content?: string | null;
        };
        /**
         * FileType
         * @enum {string}
         */
        FileType: FileType;
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /** InvoiceResponse */
        InvoiceResponse: {
            /** Id */
            id: string;
            /** Customer */
            customer: string;
            /** Status */
            status: string;
            /** Amount Due */
            amount_due: number;
            /** Amount Paid */
            amount_paid: number;
            /** Amount Remaining */
            amount_remaining: number;
            /** Currency */
            currency: string;
            /** Invoice Pdf */
            invoice_pdf?: string | null;
            /** Created */
            created: number;
            /** Due Date */
            due_date?: number | null;
            /** Paid */
            paid: boolean;
            /** Payment Intent */
            payment_intent?: string | null;
            /** Subscription */
            subscription?: string | null;
            /** Total */
            total: number;
        };
        /** Item */
        Item: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id?: string;
        };
        /** ItemCreate */
        ItemCreate: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
        };
        /** ItemPublic */
        ItemPublic: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
        };
        /** ItemUpdate */
        ItemUpdate: {
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
        };
        /** ItemsPublic */
        ItemsPublic: {
            /** Data */
            data: components["schemas"]["ItemPublic"][];
            /** Count */
            count: number;
        };
        /**
         * KBAccessLevel
         * @enum {string}
         */
        KBAccessLevel: KBAccessLevel;
        /** KBCreate */
        KBCreate: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /** @default private */
            access_level: components["schemas"]["KBAccessLevel"];
            /** @default manual */
            usage_mode: components["schemas"]["KBUsageMode"];
            /**
             * Tags
             * @default []
             */
            tags: string[];
            /**
             * Allowed Users
             * @default []
             */
            allowed_users: string[];
        };
        /** KBRead */
        KBRead: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            access_level: components["schemas"]["KBAccessLevel"];
            usage_mode: components["schemas"]["KBUsageMode"];
            /**
             * Tags
             * @default []
             */
            tags: string[];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Is Deleted */
            is_deleted: boolean;
            /** Allowed Users */
            allowed_users?: string[];
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
        };
        /** KBUpdate */
        KBUpdate: {
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            access_level?: components["schemas"]["KBAccessLevel"] | null;
            /** Tags */
            tags?: string[] | null;
            /** Allowed Users */
            allowed_users?: string[] | null;
            usage_mode?: components["schemas"]["KBUsageMode"] | null;
        };
        /**
         * KBUsageMode
         * @enum {string}
         */
        KBUsageMode: KBUsageMode;
        /** KBsRead */
        KBsRead: {
            /** Data */
            data: components["schemas"]["KBRead"][];
            /** Count */
            count: number;
        };
        /**
         * MCPServerCreateSchema
         * @description Schema for creating an MCP server
         */
        MCPServerCreateSchema: {
            /**
             * Name
             * @description Name of the MCP server
             */
            name: string;
            /**
             * Prefix
             * @description Prefix for the MCP server
             */
            prefix: string;
            /**
             * @description Transport type
             * @default streamable_http
             */
            type: components["schemas"]["MCPServerTransport"];
            /**
             * Config
             * @description Server configuration
             */
            config?: Record<string, never>;
            /**
             * Is Active
             * @description Whether the server is active
             * @default true
             */
            is_active: boolean;
            /**
             * Is Builtin
             * @description Whether this is a builtin server
             * @default false
             */
            is_builtin: boolean;
            /**
             * Tool Permissions
             * @description Tool permissions
             */
            tool_permissions?: string[];
            /**
             * Tool Enabled
             * @description Enabled tools
             */
            tool_enabled?: string[];
        };
        /**
         * MCPServerListResponseSchema
         * @description Schema for paginated MCP server list response
         */
        MCPServerListResponseSchema: {
            /** Data */
            data: components["schemas"]["MCPServerResponseSchema"][];
            /** Count */
            count: number;
        };
        /**
         * MCPServerResponseSchema
         * @description Schema for MCP server response
         */
        MCPServerResponseSchema: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Name */
            name: string;
            /** Prefix */
            prefix: string;
            type: components["schemas"]["MCPServerTransport"];
            /** Config */
            config: Record<string, never>;
            /** Is Active */
            is_active: boolean;
            /** Is Builtin */
            is_builtin: boolean;
            /** Tool List */
            tool_list: string[];
            /** Tool Permissions */
            tool_permissions: string[];
            /** Tool Enabled */
            tool_enabled: string[];
            status: components["schemas"]["MCPServerStatus"];
            /** Status Message */
            status_message: string;
            /**
             * Status Updated At
             * Format: date-time
             */
            status_updated_at: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * MCPServerStatus
         * @description MCP server status
         * @enum {string}
         */
        MCPServerStatus: MCPServerStatus;
        /**
         * MCPServerTransport
         * @description MCP server transport
         * @enum {string}
         */
        MCPServerTransport: MCPServerTransport;
        /**
         * MCPServerUpdateSchema
         * @description Schema for updating an MCP server
         */
        MCPServerUpdateSchema: {
            /**
             * Name
             * @description Name of the MCP server
             */
            name?: string | null;
            /**
             * Prefix
             * @description Prefix for the MCP server
             */
            prefix?: string | null;
            /** @description Transport type */
            type?: components["schemas"]["MCPServerTransport"] | null;
            /**
             * Config
             * @description Server configuration
             */
            config?: Record<string, never> | null;
            /**
             * Is Active
             * @description Whether the server is active
             */
            is_active?: boolean | null;
            /**
             * Tool Permissions
             * @description Tool permissions
             */
            tool_permissions?: string[] | null;
            /**
             * Tool Enabled
             * @description Enabled tools
             */
            tool_enabled?: string[] | null;
        };
        /** Memory */
        Memory: {
            /** Tags */
            tags?: string[] | null;
            /** Task */
            task?: string | null;
            /** Solution */
            solution?: string | null;
            /** Links */
            links?: string[] | null;
            /** Id */
            id: string;
            /** Agent Role */
            agent_role: string;
        };
        /** MemoryFilter */
        MemoryFilter: {
            /** Agent Roles */
            agent_roles?: string[] | null;
            /**
             * Limit
             * @default 10
             */
            limit: number;
        };
        /**
         * MemoryNode
         * @description Model for a memory node extracted from a conversation.
         */
        MemoryNode: {
            /** Tags */
            tags?: string[] | null;
            /** Task */
            task?: string | null;
            /** Solution */
            solution?: string | null;
            /** Links */
            links?: string[] | null;
        };
        /** MemoryUpdate */
        MemoryUpdate: {
            /** Id */
            id: string;
            /** Agent Role */
            agent_role: string;
            memory: components["schemas"]["MemoryNode"];
        };
        /** MemorysRead */
        MemorysRead: {
            /** Memories */
            memories: components["schemas"]["Memory"][];
            /** Count */
            count: number;
        };
        /** Message */
        Message: {
            /** Content */
            content: string;
            /**
             * Role
             * @default user
             */
            role: string;
            /**
             * Is Interrupt
             * @default false
             */
            is_interrupt: boolean;
            /** Interrupt Message */
            interrupt_message?: string | null;
            /** @default none */
            action_type: components["schemas"]["MessageActionType"];
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            /**
             * Message Metadata
             * @default {}
             */
            message_metadata: Record<string, never>;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
        };
        /**
         * MessageActionType
         * @description Enum for the type of action that can be taken by the message
         * @enum {string}
         */
        MessageActionType: MessageActionType;
        /**
         * MessageDisplayComponentPublic
         * @description Public schema for message display components
         */
        MessageDisplayComponentPublic: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            type: components["schemas"]["MessageDisplayComponentType"];
            chart_type: components["schemas"]["ChartType"] | null;
            /** Title */
            title: string | null;
            /** Description */
            description: string | null;
            /** Data */
            data: Record<string, never>;
            /** Config */
            config: Record<string, never>;
            /** Position */
            position: number;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /**
         * MessageDisplayComponentType
         * @description Enum for display component types (currently supporting only tables and charts)
         * @enum {string}
         */
        MessageDisplayComponentType: MessageDisplayComponentType;
        /**
         * MessageFeedbackCreate
         * @description Schema for creating message feedback
         */
        MessageFeedbackCreate: {
            /** @description Type of feedback (good/bad) */
            feedback_type: components["schemas"]["FeedbackType"];
            /**
             * Reason
             * @description Optional reason for the feedback, required when feedback_type is BAD
             */
            reason?: string | null;
            /**
             * Additional Comments
             * @description Additional optional comments from the user
             */
            additional_comments?: string | null;
            /**
             * Message Id
             * Format: uuid
             */
            message_id: string;
        };
        /**
         * MessageFeedbackPublic
         * @description Public schema for message feedback responses
         */
        MessageFeedbackPublic: {
            /** @description Type of feedback (good/bad) */
            feedback_type: components["schemas"]["FeedbackType"];
            /**
             * Reason
             * @description Optional reason for the feedback, required when feedback_type is BAD
             */
            reason?: string | null;
            /**
             * Additional Comments
             * @description Additional optional comments from the user
             */
            additional_comments?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Message Id
             * Format: uuid
             */
            message_id: string;
            /**
             * User Id
             * Format: uuid
             */
            user_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * MessageFeedbackUpdate
         * @description Schema for updating message feedback
         */
        MessageFeedbackUpdate: {
            feedback_type?: components["schemas"]["FeedbackType"] | null;
            /** Reason */
            reason?: string | null;
            /** Additional Comments */
            additional_comments?: string | null;
        };
        /** MessageHistoryPublic */
        MessageHistoryPublic: {
            /** Limit */
            limit: number;
            /** Has More */
            has_more: boolean;
            /**
             * Data
             * @description list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation
             * @default []
             */
            data: Record<string, never>[];
        };
        /** MessagePublic */
        MessagePublic: {
            /** Content */
            content: string;
            /** Resume */
            resume: boolean;
            /** Approve */
            approve: boolean;
            /** Restore */
            restore?: boolean | null;
            /** Message Id */
            message_id?: string | null;
            action_type?: components["schemas"]["MessageActionType"] | null;
            /** Display Components */
            display_components?: components["schemas"]["MessageDisplayComponentPublic"][] | null;
        };
        /** MessageStatistics */
        MessageStatistics: {
            /** Total Messages */
            total_messages: number;
            /** Average Response Time */
            average_response_time: number;
            /** Average Input Tokens per Message */
            average_input_tokens_per_message: number;
            /** Average Output Tokens per Message */
            average_output_tokens_per_message: number;
            /** Daily Message Volume */
            daily_message_volume: components["schemas"]["DailyMessageVolume"][];
            /** Token Distribution by Message Length */
            token_distribution_by_message_length: components["schemas"]["TokenDistributionCategory"][];
        };
        /** MetricCreate */
        MetricCreate: {
            /** Name */
            name: string;
            /** Value */
            value: number;
            /** Unit */
            unit: string;
            /**
             * Timestamp
             * Format: date-time
             */
            timestamp: string;
            type: components["schemas"]["MetricType"];
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
        };
        /** MetricPublic */
        MetricPublic: {
            /** Name */
            name: string;
            /** Value */
            value: number;
            /** Unit */
            unit: string;
            /**
             * Timestamp
             * Format: date-time
             */
            timestamp: string;
            type: components["schemas"]["MetricType"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
        };
        /** MetricRead */
        MetricRead: {
            /** Name */
            name: string;
            /** Value */
            value: number;
            /** Unit */
            unit: string;
            /**
             * Timestamp
             * Format: date-time
             */
            timestamp: string;
            type: components["schemas"]["MetricType"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
        };
        /**
         * MetricType
         * @enum {string}
         */
        MetricType: MetricType;
        /** MetricUpdate */
        MetricUpdate: {
            /** Name */
            name?: string | null;
            /** Value */
            value?: number | null;
            /** Unit */
            unit?: string | null;
            /** Timestamp */
            timestamp?: string | null;
            type?: components["schemas"]["MetricType"] | null;
        };
        /** MetricsPublic */
        MetricsPublic: {
            /** Data */
            data: components["schemas"]["MetricPublic"][];
            /** Count */
            count: number;
        };
        /** ModuleSetting */
        ModuleSetting: {
            /** Key */
            key: string;
            /**
             * Value
             * @default {}
             */
            value: Record<string, never>;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /** NewPassword */
        NewPassword: {
            /** Token */
            token: string;
            /** New Password */
            new_password: string;
        };
        /**
         * NodeType
         * @enum {string}
         */
        NodeType: NodeType;
        /**
         * NotificationList
         * @description Response model for paginated notifications list.
         *
         *     Attributes:
         *         data: List of notification items
         *         count: Total number of items available (before pagination)
         */
        NotificationList: {
            /** Data */
            data: components["schemas"]["NotificationResponse"][];
            /** Count */
            count: number;
        };
        /** NotificationResponse */
        NotificationResponse: {
            /** Title */
            title: string;
            /** Message */
            message: string;
            /** @default info */
            type: components["schemas"]["NotificationType"];
            /** @default unread */
            status: components["schemas"]["NotificationStatus"];
            /**
             * Notification Metadata
             * @description Metadata for the notification
             */
            notification_metadata?: Record<string, never>;
            /**
             * Requires Action
             * @default false
             */
            requires_action: boolean;
            /**
             * Action Url
             * @description URL for direct action
             */
            action_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * User Id
             * Format: uuid
             */
            user_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Read At */
            read_at?: string | null;
            /** Expires At */
            expires_at?: string | null;
        };
        /**
         * NotificationStatus
         * @enum {string}
         */
        NotificationStatus: NotificationStatus;
        /**
         * NotificationType
         * @enum {string}
         */
        NotificationType: NotificationType;
        /** PaymentMethodResponse */
        PaymentMethodResponse: {
            /** Id */
            id: string;
            billing_details: components["schemas"]["BillingDetails"];
            card: components["schemas"]["CardDetails"];
            /** Created */
            created: number;
            /** Customer */
            customer: string;
            /** Livemode */
            livemode: boolean;
            /** Type */
            type: string;
        };
        /** PlanChangeRequestCreate */
        PlanChangeRequestCreate: {
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Email */
            work_email: string;
            /** Work Title */
            work_title: string;
            /** Company Name */
            company_name: string;
            /** Reason */
            reason: string;
            /**
             * Current Product Id
             * Format: uuid
             */
            current_product_id: string;
            /**
             * Requested Price Id
             * Format: uuid
             */
            requested_price_id: string;
        };
        /** PlanChangeRequestResponse */
        PlanChangeRequestResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** First Name */
            first_name: string;
            /** Last Name */
            last_name: string;
            /** Work Email */
            work_email: string;
            /** Work Title */
            work_title: string;
            /** Company Name */
            company_name: string;
            /** Reason */
            reason: string;
            /** Status */
            status: string;
            /**
             * Customer Id
             * Format: uuid
             */
            customer_id: string;
            /**
             * Current Product Id
             * Format: uuid
             */
            current_product_id: string;
            /**
             * Requested Product Id
             * Format: uuid
             */
            requested_product_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /**
         * PresignedUrlInfo
         * @description Information about a generated presigned URL
         */
        PresignedUrlInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Storage Key
             * @description Storage key for the file
             */
            storage_key: string;
            /**
             * Presigned Url
             * @description Presigned URL for file upload
             */
            presigned_url: string;
        };
        /**
         * PresignedUrlRequest
         * @description Request to generate presigned URLs for file uploads
         */
        PresignedUrlRequest: {
            /**
             * Kb Id
             * @description ID of the knowledge base to upload files to
             */
            kb_id: string;
            /**
             * Files
             * @description Information about files to generate presigned URLs for
             */
            files: components["schemas"]["FileInfo"][];
        };
        /**
         * PresignedUrlResponse
         * @description Response with presigned URLs for file uploads
         */
        PresignedUrlResponse: {
            /**
             * Kb Id
             * @description Knowledge base ID
             */
            kb_id: string;
            /**
             * Presigned Urls
             * @description Generated presigned URLs
             */
            presigned_urls: components["schemas"]["PresignedUrlInfo"][];
        };
        /** PriceResponse */
        PriceResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Stripe Price Id */
            stripe_price_id: string;
            /**
             * Product Id
             * Format: uuid
             */
            product_id: string;
            /** Active */
            active: boolean;
            /** Amount */
            amount: number;
            /** Currency */
            currency: string;
            /** Interval */
            interval: string;
        };
        /** ProductResponse */
        ProductResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Stripe Product Id */
            stripe_product_id: string;
            /** Active */
            active: boolean;
            /**
             * Prices
             * @default []
             */
            prices: components["schemas"]["PriceResponse"][] | null;
            quota_definition?: components["schemas"]["QuotaDefinitionResponse"] | null;
            /** Is Custom */
            is_custom: boolean;
        };
        /** QuotaDefinitionResponse */
        QuotaDefinitionResponse: {
            /** Max Workspaces */
            max_workspaces: number;
            /** Max Members Per Workspace */
            max_members_per_workspace: number;
            /** Max Fast Requests Per Month */
            max_fast_requests_per_month: number | null;
        };
        /** QuotaInfo */
        QuotaInfo: {
            /** Quota Used */
            quota_used: number;
            /** Quota Limit */
            quota_limit: number;
            /** Quota Remaining */
            quota_remaining: number;
            /** Usage Percentage */
            usage_percentage: number;
        };
        /** RecommendationCreate */
        RecommendationCreate: {
            type: components["schemas"]["RecommendationType"];
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Potential Savings */
            potential_savings: number;
            /** Effort */
            effort: string;
            /** Risk */
            risk: string;
            /** @default pending */
            status: components["schemas"]["RecommendationStatus"];
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
        };
        /** RecommendationOveralPublic */
        RecommendationOveralPublic: {
            /** Total Resource Scanned */
            total_resource_scanned: number;
            /** Total Well Optimized */
            total_well_optimized: number;
            /** Total Optimization Opportunities */
            total_optimization_opportunities: number;
            /** Total Estimated Saving Amount */
            total_estimated_saving_amount: number;
        };
        /** RecommendationPublic */
        RecommendationPublic: {
            type: components["schemas"]["RecommendationType"];
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Potential Savings */
            potential_savings: number;
            /** Effort */
            effort: string;
            /** Risk */
            risk: string;
            /** @default pending */
            status: components["schemas"]["RecommendationStatus"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Resource Id
             * Format: uuid
             */
            resource_id: string;
            resource: components["schemas"]["ResourcePublic"];
        };
        /**
         * RecommendationStatus
         * @enum {string}
         */
        RecommendationStatus: RecommendationStatus;
        /**
         * RecommendationType
         * @enum {string}
         */
        RecommendationType: RecommendationType;
        /** RecommendationUpdate */
        RecommendationUpdate: {
            type?: components["schemas"]["RecommendationType"] | null;
            /** Title */
            title?: string | null;
            /** Description */
            description?: string | null;
            /** Potential Savings */
            potential_savings?: number | null;
            /** Effort */
            effort?: string | null;
            /** Risk */
            risk?: string | null;
            status?: components["schemas"]["RecommendationStatus"] | null;
        };
        /** RecommendationsPublic */
        RecommendationsPublic: {
            /** Data */
            data: components["schemas"]["RecommendationPublic"][];
            /** Count */
            count: number;
        };
        /** Report */
        Report: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /**
             * Sections
             * @default {}
             */
            sections: Record<string, never>;
            /**
             * Executive Summary
             * @default {}
             */
            executive_summary: Record<string, never>;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /** ResendActivationRequest */
        ResendActivationRequest: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Captcha Token */
            captcha_token: string;
        };
        /** Resource */
        Resource: {
            /** Name */
            name: string;
            /** Arn */
            arn: string;
            /**
             * Tags
             * @default {}
             */
            tags: Record<string, never>;
            /**
             * Configurations
             * @default {}
             */
            configurations: Record<string, never>;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /** Region */
            region: string;
            /** @default found */
            status: components["schemas"]["ResourceStatus"];
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            /**
             * Is Active
             * @default true
             */
            is_active: boolean;
        };
        /** ResourceCreate */
        ResourceCreate: {
            /** Name */
            name: string;
            /** Arn */
            arn: string;
            /**
             * Tags
             * @default {}
             */
            tags: Record<string, never>;
            /**
             * Configurations
             * @default {}
             */
            configurations: Record<string, never>;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /** Region */
            region: string;
            /** @default found */
            status: components["schemas"]["ResourceStatus"];
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
        };
        /** ResourcePublic */
        ResourcePublic: {
            /** Name */
            name: string;
            /** Arn */
            arn: string;
            /**
             * Tags
             * @default {}
             */
            tags: Record<string, never>;
            /**
             * Configurations
             * @default {}
             */
            configurations: Record<string, never>;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /** Region */
            region: string;
            /** @default found */
            status: components["schemas"]["ResourceStatus"];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            workspace: components["schemas"]["WorkspacePublic"];
            /** Total Recommendation */
            total_recommendation: number;
            /** Total Potential Saving */
            total_potential_saving: number;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
        };
        /** ResourceRead */
        ResourceRead: {
            /** Name */
            name: string;
            /** Arn */
            arn: string;
            /** Tags */
            tags: {
                [key: string]: string;
            };
            /** Configurations */
            configurations: Record<string, never>;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /** Region */
            region: string;
            /** @default found */
            status: components["schemas"]["ResourceStatus"];
            /** Recommendations */
            recommendations: components["schemas"]["RecommendationPublic"][];
            /** Metrics */
            metrics?: {
                [key: string]: components["schemas"]["MetricRead"][];
            } | null;
        };
        /** ResourceSavingsReport */
        ResourceSavingsReport: {
            /** Rds Savings */
            rds_savings: components["schemas"]["ChartDataPoint"][];
            /** Ec2 Savings */
            ec2_savings: components["schemas"]["ChartDataPoint"][];
            /** Total Rds Savings */
            total_rds_savings: number;
            /** Total Ec2 Savings */
            total_ec2_savings: number;
        };
        /**
         * ResourceStatus
         * @enum {string}
         */
        ResourceStatus: ResourceStatus;
        /**
         * ResourceType
         * @enum {string}
         */
        ResourceType: ResourceType;
        /** ResourceUpdate */
        ResourceUpdate: {
            /** Name */
            name?: string | null;
            /** Arn */
            arn?: string | null;
            /**
             * Tags
             * @default {}
             */
            tags: Record<string, never>;
            /**
             * Configurations
             * @default {}
             */
            configurations: Record<string, never>;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /** Region */
            region: string;
            /** @default found */
            status: components["schemas"]["ResourceStatus"];
        };
        /** ResourcesPublic */
        ResourcesPublic: {
            /** Data */
            data: components["schemas"]["ResourcePublic"][];
            /** Count */
            count: number;
        };
        /** RetrieverConfig */
        RetrieverConfig: {
            /**
             * Numberofresults
             * @default 4
             */
            numberOfResults: number;
            /**
             * Overridesearchtype
             * @default SEMANTIC
             */
            overrideSearchType: RetrieverConfigOverrideSearchType | null;
        };
        /**
         * RunModeEnum
         * @enum {string}
         */
        RunModeEnum: RunModeEnum;
        /** SavingSummaryReport */
        SavingSummaryReport: {
            /** Potential Savings */
            potential_savings: number;
            /** Potential Savings Percentage Change */
            potential_savings_percentage_change: number;
            /** Save Opportunities */
            save_opportunities: number;
            /** Save Opportunities Percentage Change */
            save_opportunities_percentage_change: number;
            /** Total Saved */
            total_saved: number;
            /** Total Saved Percentage Change */
            total_saved_percentage_change: number;
            /** Active Saving */
            active_saving: number;
            /** Active Saving Percentage Change */
            active_saving_percentage_change: number;
        };
        /** ScriptExecutionResponse */
        ScriptExecutionResponse: {
            /** Status */
            status: string;
            /** Result */
            result?: string | null;
        };
        /** SearchResponse */
        SearchResponse: {
            /** Query */
            query: string;
            /** Results */
            results: components["schemas"]["Document"][];
            /** Total Found */
            total_found: number;
            /** Execution Time */
            execution_time: number;
        };
        /** ServiceSavingsData */
        ServiceSavingsData: {
            /** Service */
            service: string;
            /** Savings */
            savings: number;
            /** Percentage */
            percentage: number;
        };
        /** ServiceSavingsReport */
        ServiceSavingsReport: {
            /** Data */
            data: components["schemas"]["ServiceSavingsData"][];
            /** Total Savings */
            total_savings: number;
        };
        /** Setting */
        Setting: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /** @default AWS */
            provider_name: components["schemas"]["CloudProvider"];
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /**
             * Cron Patterns
             * @default []
             */
            cron_patterns: string[];
        };
        /** ShareResponse */
        ShareResponse: {
            /**
             * Share Id
             * Format: uuid
             */
            share_id: string;
            /** Is Shared */
            is_shared: boolean;
            /**
             * Shared At
             * Format: date-time
             */
            shared_at: string;
            /**
             * Shared By
             * Format: uuid
             */
            shared_by: string;
        };
        /** StreamResponse */
        StreamResponse: {
            /** Type */
            type: string;
            /** Content */
            content?: string | null;
            /** Message Id */
            message_id?: string | null;
        };
        /** SubscriptionStatus */
        SubscriptionStatus: {
            /** Id */
            id: string;
            /** Customer Id */
            customer_id: string;
            /** Status */
            status: string;
            /**
             * Current Period End
             * Format: date-time
             */
            current_period_end: string;
            /** Cancel At */
            cancel_at?: string | null;
            /** Product Name */
            product_name: string;
            /** Product Id */
            product_id?: string | null;
            /** Price Amount */
            price_amount: number;
            /** Price Currency */
            price_currency: string;
            /** Price Interval */
            price_interval: string;
        };
        /** SummaryResponse */
        SummaryResponse: {
            /** Query */
            query: string;
            /** Summary */
            summary: string;
            /** Sources */
            sources: unknown[];
            /** Citations */
            citations: components["schemas"]["CitationMetadata"][];
            /** Execution Time */
            execution_time: number;
        };
        /**
         * TaskCategoryEnum
         * @description Enumeration of possible task categories.
         * @enum {string}
         */
        TaskCategoryEnum: TaskCategoryEnum;
        /**
         * TaskContinueRequest
         * @description Schema for task continue request.
         */
        TaskContinueRequest: {
            /**
             * Message
             * @description Message to continue the task with
             */
            message: string;
            /**
             * Approve
             * @description Whether to approve the task continuation
             */
            approve: boolean;
        };
        /**
         * TaskContinueResponse
         * @description Schema for task continue response.
         */
        TaskContinueResponse: {
            /**
             * Task History Id
             * Format: uuid
             * @description Task history ID that was continued
             */
            task_history_id: string;
            /**
             * Status
             * @description Status of the continue operation
             */
            status: string;
            /**
             * Celery Task Id
             * @description Celery task ID for tracking
             */
            celery_task_id?: string | null;
        };
        /**
         * TaskCouldEnum
         * @enum {string}
         */
        TaskCouldEnum: TaskCouldEnum;
        /**
         * TaskCreate
         * @description Schema for creating a new task.
         */
        TaskCreate: {
            /** Title */
            title: string;
            /**
             * Description
             * @default
             */
            description: string | null;
            /** @default 0 */
            priority: components["schemas"]["TaskPriority"];
            /** Tags */
            tags?: string[];
            /** Schedule */
            schedule?: string | null;
            /** Agent Config */
            agent_config?: Record<string, never>;
        };
        /**
         * TaskDeleteResponse
         * @description Schema for task delete response.
         */
        TaskDeleteResponse: {
            /**
             * Status
             * @default success
             */
            status: string;
        };
        /**
         * TaskExecutionStatus
         * @description Enumeration of execution statuses for task.
         *
         *     Attributes:
         *         RUNNING: Currently executing
         *         SUCCEEDED: Successfully completed
         *         FAILED: Execution failed
         *         CANCELLED: Execution cancelled
         *         REQUIRED_APPROVAL: Execution requires approval
         * @enum {string}
         */
        TaskExecutionStatus: TaskExecutionStatus;
        /**
         * TaskHistory
         * @description Execution history of a task conversation.
         */
        TaskHistory: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Task Id
             * Format: uuid
             */
            task_id: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /** @description Current task status */
            status: components["schemas"]["TaskExecutionStatus"];
            /**
             * Message
             * @description Message from the task execution: error or required action
             */
            message?: string | null;
            /**
             * Celery Task Id
             * @description Celery task ID associated with the task
             */
            celery_task_id?: string | null;
            /**
             * Start Time
             * Format: date-time
             * @description Timestamp when the task history was started
             */
            start_time?: string;
            /**
             * End Time
             * @description Timestamp when the task history was ended
             */
            end_time?: string | null;
            /**
             * Run Time
             * @description Time taken to run the task
             */
            run_time?: number | null;
        };
        /**
         * TaskList
         * @description Schema for paginated task list.
         */
        TaskList: {
            /** Data */
            data?: components["schemas"]["TaskResponse"][];
            /**
             * Total
             * @default 0
             */
            total: number;
        };
        /**
         * TaskPriority
         * @description Enumeration of task priority levels.
         *
         *     Attributes:
         *         LOW (0): Regular priority, no urgency
         *         MEDIUM (1): Moderate priority, should be done soon
         *         HIGH (2): High priority, urgent attention needed
         *         CRITICAL (3): Critical priority, requires immediate attention
         * @enum {integer}
         */
        TaskPriority: TaskPriority;
        /**
         * TaskResponse
         * @description Schema for task response.
         */
        TaskResponse: {
            /** Title */
            title: string;
            /**
             * Description
             * @default
             */
            description: string | null;
            /** @default 0 */
            priority: components["schemas"]["TaskPriority"];
            /** Tags */
            tags?: string[];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            scheduled_status?: components["schemas"]["TaskScheduledStatus"] | null;
            execution_status?: components["schemas"]["TaskExecutionStatus"] | null;
            /** Error */
            error?: string | null;
            /** Last Run */
            last_run?: string | null;
            /** Next Run */
            next_run?: string | null;
            /** Schedule */
            schedule?: string | null;
            /** Agent Config */
            agent_config?: Record<string, never>;
            /**
             * Enable
             * @default true
             */
            enable: boolean;
            /** Task History */
            task_history?: components["schemas"]["TaskHistory"][];
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /**
             * Created By
             * Format: uuid
             */
            created_by: string;
            /**
             * Updated By
             * Format: uuid
             */
            updated_by: string;
        };
        /**
         * TaskScheduledStatus
         * @description Enumeration of scheduled statuses for task.
         * @enum {string}
         */
        TaskScheduledStatus: TaskScheduledStatus;
        /**
         * TaskServiceEnum
         * @description Enumeration of possible task services.
         * @enum {string}
         */
        TaskServiceEnum: TaskServiceEnum;
        /**
         * TaskStatusResponse
         * @description Response schema for task status operations
         */
        TaskStatusResponse: {
            /**
             * Task Id
             * @description Celery task ID
             */
            task_id: string;
            /** @description Task status (PENDING, PROGRESS, SUCCESS, FAILURE) */
            status: components["schemas"]["AsyncTaskStatus"];
            /**
             * Progress
             * @description Progress percentage (0-100)
             * @default 0
             */
            progress: number;
            /**
             * Result
             * @description Task result if completed
             */
            result?: Record<string, never> | null;
            /**
             * Error
             * @description Error message if failed
             */
            error?: string | null;
            /**
             * Status Message
             * @description Human-readable status message
             */
            status_message?: string | null;
        };
        /**
         * TaskStopResponse
         * @description Schema for task stop response.
         */
        TaskStopResponse: {
            /**
             * Task Id
             * Format: uuid
             */
            task_id: string;
            /**
             * Conversation Id
             * Format: uuid
             */
            conversation_id: string;
            /** Status */
            status: string;
        };
        /** TaskTemplateCreate */
        TaskTemplateCreate: {
            /** Task */
            task: string;
            /** @default OTHER */
            category: components["schemas"]["TaskCategoryEnum"] | null;
            /** @default OTHER */
            service: components["schemas"]["TaskServiceEnum"] | null;
            /**
             * Service Name
             * @default
             */
            service_name: string | null;
            cloud: components["schemas"]["TaskCouldEnum"];
            run_mode: components["schemas"]["RunModeEnum"];
            /** Schedule */
            schedule?: string | null;
            /** Context */
            context: string;
        };
        /** TaskTemplateList */
        TaskTemplateList: {
            /** Data */
            data: components["schemas"]["TaskTemplateResponse"][];
            /** Total */
            total: number;
        };
        /** TaskTemplateResponse */
        TaskTemplateResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Task */
            task: string;
            category: components["schemas"]["TaskCategoryEnum"];
            service: components["schemas"]["TaskServiceEnum"];
            /** Service Name */
            service_name: string;
            cloud: components["schemas"]["TaskCouldEnum"];
            run_mode: components["schemas"]["RunModeEnum"];
            /** Schedule */
            schedule: string | null;
            /** Context */
            context: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at: string | null;
        };
        /** TaskTemplateUpdate */
        TaskTemplateUpdate: {
            /** Task */
            task?: string | null;
            category?: components["schemas"]["TaskCategoryEnum"] | null;
            service?: components["schemas"]["TaskServiceEnum"] | null;
            run_mode?: components["schemas"]["RunModeEnum"] | null;
            /** Schedule */
            schedule?: string | null;
            /** Context */
            context?: string | null;
        };
        /**
         * TaskUpdate
         * @description Schema for updating an existing task.
         */
        TaskUpdate: {
            /** Title */
            title?: string | null;
            /**
             * Description
             * @default
             */
            description: string | null;
            priority?: components["schemas"]["TaskPriority"] | null;
            /** Tags */
            tags?: string[] | null;
            /** Schedule */
            schedule?: string | null;
            /** Agent Config */
            agent_config?: Record<string, never>;
        };
        /** Token */
        Token: {
            /** Access Token */
            access_token: string;
            /**
             * Token Type
             * @default bearer
             */
            token_type: string;
            /** Workspace Id */
            workspace_id?: string | null;
            /**
             * Is First Login
             * @default false
             */
            is_first_login: boolean;
            /**
             * Slack Oauth
             * @default false
             */
            slack_oauth: boolean;
            /** App Id */
            app_id?: string | null;
            /** Team Id */
            team_id?: string | null;
        };
        /** TokenDistributionCategory */
        TokenDistributionCategory: {
            /** Category */
            category: string;
            /** Percentage */
            percentage: number;
        };
        /** TokenUsageCreate */
        TokenUsageCreate: {
            /**
             * Message ID
             * Format: uuid
             * @description Unique identifier of the associated message
             */
            message_id: string;
            /**
             * Input Tokens
             * @description Number of tokens in the input text
             * @example 100
             */
            input_tokens: number;
            /**
             * Output Tokens
             * @description Number of tokens in the output text
             * @example 150
             */
            output_tokens: number;
            /**
             * Model ID
             * @description Identifier of the AI model used
             * @example gpt-4
             */
            model_id: string;
        };
        /**
         * TokenUsageResponse
         * @description Schema for token usage response.
         *
         *     Attributes:
         *         id: Unique identifier for the usage record
         *         message_id: ID of the associated message
         *         input_tokens: Number of tokens in input text
         *         output_tokens: Number of tokens in output text
         *         model_id: ID of the AI model used
         *         total_tokens: Total number of tokens used
         *         created_at: Timestamp of record creation
         */
        TokenUsageResponse: {
            /**
             * ID
             * Format: uuid
             * @description Unique identifier for the usage record
             */
            id: string;
            /**
             * Message ID
             * Format: uuid
             * @description ID of the associated message
             */
            message_id: string;
            /**
             * Input Tokens
             * @description Number of tokens in the input text
             */
            input_tokens: number;
            /**
             * Output Tokens
             * @description Number of tokens in the output text
             */
            output_tokens: number;
            /**
             * Model ID
             * @description Identifier of the AI model used
             */
            model_id: string;
            /**
             * Workspace ID
             * Format: uuid
             * @description ID of the workspace
             */
            workspace_id: string;
            /**
             * Created At
             * Format: date-time
             * @description Timestamp of record creation
             */
            created_at: string;
            /**
             * Updated At
             * @description Timestamp of last update
             */
            updated_at?: string | null;
            /**
             * Total Tokens
             * @description Calculate total tokens from input and output tokens.
             */
            readonly total_tokens: number;
        };
        /** TopSavingsReport */
        TopSavingsReport: {
            /** Data */
            data: components["schemas"]["RecommendationPublic"][];
        };
        /** URLsUploadRequest */
        URLsUploadRequest: {
            /**
             * Urls
             * @description URLs to crawl (required if source_type is website)
             */
            urls?: string[] | null;
            /**
             * Deep Crawls
             * @description Whether to enable deep crawling for each URL
             */
            deep_crawls?: boolean[] | null;
        };
        /** UpdatePassword */
        UpdatePassword: {
            /** Current Password */
            current_password: string;
            /** New Password */
            new_password: string;
        };
        /** UploadCreate */
        UploadCreate: {
            /** Filename */
            filename: string;
            /** File Size */
            file_size: number;
            /** File Type */
            file_type: string;
        };
        /** UploadPublic */
        UploadPublic: {
            /** Filename */
            filename: string;
            /** File Size */
            file_size: number;
            /** File Type */
            file_type: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            status: components["schemas"]["UploadStatus"];
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
        };
        /** UploadResponse */
        UploadResponse: {
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Upload Url */
            upload_url: string;
            /** Expires In */
            expires_in: number;
        };
        /**
         * UploadStatus
         * @enum {string}
         */
        UploadStatus: UploadStatus;
        /**
         * UploadedFileInfo
         * @description Information about a successfully uploaded file
         */
        UploadedFileInfo: {
            /**
             * File Id
             * @description Client-side ID for tracking this file
             */
            file_id: string;
            /**
             * Filename
             * @description Original filename
             */
            filename: string;
            /**
             * Storage Key
             * @description Storage key for the uploaded file
             */
            storage_key: string;
            /**
             * Content Type
             * @description File MIME type
             */
            content_type?: string | null;
            /**
             * File Size
             * @description File size in bytes
             */
            file_size?: number | null;
        };
        /**
         * UsageQuotaResponse
         * @description Response schema for usage quota information.
         */
        UsageQuotaResponse: {
            /**
             * ID
             * Format: uuid
             */
            id: string;
            /**
             * User ID
             * Format: uuid
             */
            user_id: string;
            /** Quota Used Messages */
            quota_used_messages: number;
            /** Quota Used Tokens */
            quota_used_tokens: number;
            /**
             * Reset At
             * Format: date-time
             */
            reset_at: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Updated At */
            updated_at?: string | null;
        };
        /**
         * UsageStatistics
         * @description Response schema for usage statistics.
         */
        UsageStatistics: {
            /** Input Tokens */
            input_tokens: number;
            /** Output Tokens */
            output_tokens: number;
            /** Total Tokens */
            total_tokens: number;
            /** Quota Limit */
            quota_limit: number;
            /** Quota Used */
            quota_used: number;
            /** Quota Remaining */
            quota_remaining: number;
            /** Usage Percentage */
            usage_percentage: number;
            /** Daily Token Usage */
            daily_token_usage: components["schemas"]["DailyTokenUsage"][];
            /** Agent Type Stats */
            agent_type_stats: components["schemas"]["AgentTypeUsage"][];
        };
        /** UserCreate */
        UserCreate: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /** Password */
            password: string;
        };
        /** UserDetail */
        UserDetail: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Workspaces */
            workspaces?: components["schemas"]["Workspace"][] | null;
            /** Own Workspaces */
            own_workspaces?: components["schemas"]["Workspace"][] | null;
            /** Items */
            items?: components["schemas"]["Item"][] | null;
        };
        /** UserPublic */
        UserPublic: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
        };
        /** UserRegister */
        UserRegister: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Password */
            password: string;
            /** Full Name */
            full_name?: string | null;
        };
        /** UserUpdate */
        UserUpdate: {
            /** Email */
            email?: string | null;
            /**
             * Is Active
             * @default false
             */
            is_active: boolean;
            /**
             * Is Email Verified
             * @default false
             */
            is_email_verified: boolean;
            /**
             * Is Superuser
             * @default false
             */
            is_superuser: boolean;
            /** Last Login Time */
            last_login_time?: string | null;
            /** Full Name */
            full_name?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
            /** Password */
            password?: string | null;
        };
        /** UserUpdateMe */
        UserUpdateMe: {
            /** Full Name */
            full_name?: string | null;
            /** Email */
            email?: string | null;
            /** Avatar Url */
            avatar_url?: string | null;
        };
        /** UsersPublic */
        UsersPublic: {
            /** Data */
            data: components["schemas"]["UserPublic"][];
            /** Count */
            count: number;
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /** WorkflowCreate */
        WorkflowCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
        };
        /** WorkflowNodeCreate */
        WorkflowNodeCreate: {
            type: components["schemas"]["NodeType"];
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Position */
            position: number;
            /**
             * Data
             * @default {}
             */
            data: Record<string, never>;
            /**
             * Workflow Id
             * Format: uuid
             */
            workflow_id: string;
            /** Parent Id */
            parent_id?: string | null;
        };
        /** WorkflowNodePublic */
        WorkflowNodePublic: {
            type: components["schemas"]["NodeType"];
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Position */
            position: number;
            /**
             * Data
             * @default {}
             */
            data: Record<string, never>;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            status: components["schemas"]["WorkflowStatus"];
        };
        /** WorkflowNodeUpdate */
        WorkflowNodeUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Position */
            position?: number | null;
        };
        /** WorkflowPublic */
        WorkflowPublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Nodes */
            nodes: components["schemas"]["WorkflowNodePublic"][];
            /** @default created */
            status: components["schemas"]["WorkflowStatus"];
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /**
         * WorkflowStatus
         * @enum {string}
         */
        WorkflowStatus: WorkflowStatus;
        /** WorkflowUpdate */
        WorkflowUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Workspace Id */
            workspace_id?: string | null;
        };
        /** WorkflowsPublic */
        WorkflowsPublic: {
            /** Data */
            data: components["schemas"]["WorkflowPublic"][];
            /** Count */
            count: number;
        };
        /** Workspace */
        Workspace: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            /**
             * Is Default
             * @default false
             */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
        };
        /** WorkspaceCreate */
        WorkspaceCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Owner Id */
            owner_id?: string | null;
        };
        /** WorkspaceDetail */
        WorkspaceDetail: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
            aws_account?: components["schemas"]["AWSAccountDetail"] | null;
            settings: components["schemas"]["WorkspaceSetting"] | null;
            provider_settings: components["schemas"]["Setting"];
        };
        /** WorkspacePublic */
        WorkspacePublic: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Default */
            is_default: boolean;
            /**
             * Is Deleted
             * @default false
             */
            is_deleted: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at?: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at?: string;
        };
        /**
         * WorkspaceSetting
         * @description Settings for a workspace
         */
        WorkspaceSetting: {
            /**
             * Id
             * Format: uuid
             */
            id?: string;
            /**
             * Workspace Id
             * Format: uuid
             */
            workspace_id: string;
            /**
             * Regions
             * @default []
             */
            regions: string[];
            /**
             * Types
             * @default []
             */
            types: string[];
            /** Cron Pattern */
            cron_pattern: string;
        };
        /** WorkspaceUpdate */
        WorkspaceUpdate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
        };
        /** WorkspacesPublic */
        WorkspacesPublic: {
            /** Data */
            data: components["schemas"]["WorkspacePublic"][];
            /** Count */
            count: number;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    "login-login_access_token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_login-login_access_token"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-test_token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
        };
    };
    "login-recover_password": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-reset_password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["NewPassword"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "login-recover_password_html_content": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                email: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "text/html": string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_users": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsersPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-create_user": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDetail"];
                };
            };
        };
    };
    "users-delete_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
        };
    };
    "users-update_user_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdateMe"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-update_password_me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdatePassword"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-read_user_by_id": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-delete_user": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-update_user": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "users-switch_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "utils-test_email": {
        parameters: {
            query: {
                email_to: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "utils-health_check": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
        };
    };
    "utils-publish_message": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["Message"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "utils-enqueue_message": {
        parameters: {
            query: {
                task_name: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["Message"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "items-read_items": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ItemsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "items-create_item": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ItemCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ItemPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "items-read_item": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ItemPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "items-update_item": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ItemUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ItemPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "items-delete_item": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-read_aws_accounts": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-create_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AWSAccountCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-read_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-update_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AWSAccountUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AWSAccountPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "aws-accounts-delete_aws_account": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-read_resources": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                name?: string;
                resource_type?: string[];
                status?: string[];
                region?: string[];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourcesPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-create_resource": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ResourceCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourcePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-read_resource": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourceRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-update_resource": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ResourceUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourcePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "resources-delete_resource": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "metrics-read_metrics": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                resource_id?: string | null;
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MetricsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "metrics-create_metric": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MetricCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MetricPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "metrics-read_metric": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MetricPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "metrics-update_metric": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MetricUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MetricPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "metrics-delete_metric": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-get_recomendation_overal": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationOveralPublic"];
                };
            };
        };
    };
    "recommendations-read_recommendations": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
                resource_id?: string[];
                resource_type?: string[];
                recommendation_type?: string[];
                status?: string[];
                start_date?: string | null;
                end_date?: string | null;
                order_by?: string | null;
                order_direction?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-create_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RecommendationCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-read_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-update_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RecommendationUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-delete_recommendation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "recommendations-update_recommendation_status": {
        parameters: {
            query: {
                status: components["schemas"]["RecommendationStatus"];
            };
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-read_workflows": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-create_workflow": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-read_workflow": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-update_workflow": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-delete_workflow": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-create_workflow_from_template": {
        parameters: {
            query: {
                workspace_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-read_workflow_nodes": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowNodePublic"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-create_workflow_node": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowNodeCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowNodePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-read_workflow_node": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
                node_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowNodePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-update_workflow_node": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
                node_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowNodeUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowNodePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-delete_workflow_node": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
                node_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-run_workflow_node": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
                node_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workflows-run_workflow": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-read_workspaces": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacesPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-create_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkspaceCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-read_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspaceDetail"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-update_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkspaceUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkspacePublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "workspaces-delete_workspace": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tools-script_execution": {
        parameters: {
            query: {
                script: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ScriptExecutionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "console-proxy-get_files": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FileListResponse"];
                };
            };
        };
    };
    "console-proxy-get_file_content": {
        parameters: {
            query: {
                /** @description File path */
                path: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FileContentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-read_agents": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-create_agent": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-read_agent": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-update_agent": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-delete_agent": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agents-init_default_agents": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-list_tasks": {
        parameters: {
            query?: {
                execution_status?: components["schemas"]["TaskExecutionStatus"] | null;
                skip?: number;
                limit?: number;
                include_history?: boolean;
                history_limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-create_task": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-get_task": {
        parameters: {
            query?: {
                include_history?: boolean;
                history_limit?: number;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-update_task": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-delete_task": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskDeleteResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-update_task_enable": {
        parameters: {
            query?: {
                enable?: boolean;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-stop_task_execution": {
        parameters: {
            query: {
                conversation_id: string;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStopResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-get_task_progress": {
        parameters: {
            query: {
                conversation_id: string;
            };
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskExecutionStatus"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "tasks-continue_interrupted_task": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_history_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskContinueRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskContinueResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-get_conversations": {
        parameters: {
            query?: {
                agent_id?: string | null;
                resource_id?: string | null;
                model_provider?: string | null;
                /** @description Number of records to skip for pagination */
                skip?: number;
                /** @description Maximum number of records to return */
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-create_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConversationCreateRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-get_messages_history": {
        parameters: {
            query?: {
                limit?: number;
            };
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageHistoryPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-chat_stream": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessagePublic"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StreamResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-rename_conversation": {
        parameters: {
            query: {
                name: string;
            };
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "autonomous-agents-delete_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "memory-update_memory": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MemoryUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "memory-get_memory": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MemoryFilter"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MemorysRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "memory-delete_memory": {
        parameters: {
            query: {
                id: string;
                agent_role: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-create_sample_resources": {
        parameters: {
            query?: {
                resource_count?: number;
                metrics_per_resource?: number;
                days_back?: number;
            };
            header?: never;
            path: {
                resource_type: components["schemas"]["ResourceType"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-create_sample_metrics": {
        parameters: {
            query?: {
                num_points?: number;
                days_back?: number;
            };
            header?: never;
            path: {
                resource_type: components["schemas"]["ResourceType"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "sample-data-create_sample_recommendations": {
        parameters: {
            query?: {
                total_record?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecommendationsPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "google-google_login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "google-google_callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
        };
    };
    "quotas-create_usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TokenUsageCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TokenUsageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_messages_statistics": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageStatistics"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_usage_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-create_usage_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-reset_user_quota": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageQuotaResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_usage_statistics": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsageStatistics"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "quotas-get_quota_info": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                user_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["QuotaInfo"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_savings_summary": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
                previous_start_date?: string | null;
                previous_end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SavingSummaryReport"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_savings_by_resource": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ResourceSavingsReport"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_top_potential_savings": {
        parameters: {
            query?: {
                limit?: number;
                /** @description Start date in ISO format */
                start_date?: string | null;
                /** @description End date in ISO format */
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TopSavingsReport"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_savings_by_service": {
        parameters: {
            query?: {
                start_date?: string | null;
                end_date?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ServiceSavingsReport"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "reports-get_report_by_conversation": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Report"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-generate": {
        parameters: {
            query: {
                input: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-list_templates": {
        parameters: {
            query?: {
                category?: components["schemas"]["TaskCategoryEnum"][] | null;
                services?: components["schemas"]["TaskServiceEnum"][] | null;
                include_defaults?: boolean;
                skip?: number;
                limit?: number;
                search_query?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-create_template": {
        parameters: {
            query?: {
                is_default?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskTemplateCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-get_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-update_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskTemplateUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskTemplateResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "task_templates-delete_template": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                template_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base_runtime-search": {
        parameters: {
            query: {
                query: string;
                kb_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RetrieverConfig"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SearchResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    "knowledge_base_runtime-summarize": {
        parameters: {
            query: {
                query: string;
                kb_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RetrieverConfig"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SummaryResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    "files-create_upload_url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UploadCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UploadResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "files-check_upload_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UploadPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_kbs": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
                access_level?: string | null;
                usage_mode?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBsRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-create_kb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KBCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_available_users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AvailableUsersCurrentWorkspace"];
                };
            };
        };
    };
    "knowledge_base-get_point_usage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    "knowledge_base-get_kb_by_id": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-update_kb": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KBUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-delete_kb": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-generate_presigned_urls": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PresignedUrlRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PresignedUrlResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-confirm_file_uploads": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConfirmUploadsRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-list_documents": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                search?: string | null;
            };
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DocumentsKBRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-upload_urls": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["URLsUploadRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_document_content": {
        parameters: {
            query: {
                object_name: string;
            };
            header?: never;
            path: {
                kb_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-delete_document": {
        parameters: {
            query: {
                object_name: string;
            };
            header?: never;
            path: {
                kb_id: string;
                document_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "knowledge_base-get_task_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connectors-list_connectors": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectorList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connectors-create_connector": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConnectorCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connectors-get_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connectors-update_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ConnectorUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "connectors-delete_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_available_plans": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ProductResponse"][];
                };
            };
        };
    };
    "subscriptions-get_user_subscription_status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SubscriptionStatus"][];
                };
            };
        };
    };
    "subscriptions-get_workspace_subscription_status": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workspace_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SubscriptionStatus"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-create_checkout_session": {
        parameters: {
            query: {
                price_id: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CheckoutSessionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_user_payment_methods": {
        parameters: {
            query?: {
                payment_type?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PaymentMethodResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-get_user_invoices": {
        parameters: {
            query?: {
                limit?: number;
                status?: string | null;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InvoiceResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-submit_enterprise_enquiry": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EnterpriseEnquiryRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EnterpriseEnquiryMessageResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-submit_plan_change_request": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PlanChangeRequestCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PlanChangeRequestResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "subscriptions-webhook": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "subscriptions-cancel_subscription": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    "module_setting-get_module_settings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ModuleSetting"][];
                };
            };
        };
    };
    "alerts-get_alert_status_summary": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertStatusSummary"];
                };
            };
        };
    };
    "alerts-list_alerts": {
        parameters: {
            query?: {
                severity?: components["schemas"]["AlertSeverity"] | null;
                status?: components["schemas"]["AlertStatus"] | null;
                /** @description Field to sort by */
                sort_by?: string;
                /** @description Sort in descending order */
                sort_desc?: boolean;
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-create_alert": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AlertCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-get_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-update_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AlertUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-delete_alert": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-update_alert_status": {
        parameters: {
            query: {
                status: components["schemas"]["AlertStatus"];
            };
            header?: never;
            path: {
                alert_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AlertResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "alerts-mark_all_alerts_acknowledged": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    "auth-register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserRegister"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "auth-activate_account": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                token: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResult"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "auth-resend_activation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ResendActivationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActivationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-list_notifications": {
        parameters: {
            query?: {
                requires_action?: boolean | null;
                timeframe?: string | null;
                skip?: number;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["Body_notifications-list_notifications"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["NotificationList"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-mark_notification_read": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                notification_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["NotificationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "notifications-mark_all_notifications_read": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["NotificationType"][] | null;
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-get_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"] | null;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-update_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageFeedbackUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-delete_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                message_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "message-feedback-create_message_feedback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MessageFeedbackCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageFeedbackPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "mcp-server-get_mcp_servers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerListResponseSchema"];
                };
            };
        };
    };
    "mcp-server-create_mcp_server": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MCPServerCreateSchema"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "mcp-server-get_mcp_server": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                server_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "mcp-server-update_mcp_server": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                server_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MCPServerUpdateSchema"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "mcp-server-delete_mcp_server": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                server_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Message"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "mcp-server-refresh_mcp_server": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                server_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MCPServerResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "builtin-connectors-list_workspace_connectors": {
        parameters: {
            query?: {
                skip?: number;
                limit?: number;
                active_only?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConnectorWithStatusResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "builtin-connectors-update_connector_for_workspace": {
        parameters: {
            query: {
                is_active: boolean;
            };
            header?: never;
            path: {
                connector_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "builtin-connectors-update_connector_permission": {
        parameters: {
            query: {
                required_permission: boolean;
            };
            header?: never;
            path: {
                connector_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-connectors-create_agent_connector": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentConnectorCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-connectors-get_agent_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-connectors-update_agent_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentConnectorUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectorResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-connectors-delete_agent_connector": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-connectors-get_agent_connectors_by_workspace_id": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentConnectorResponse"][];
                };
            };
        };
    };
    "agent-context-get_agent_context": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentContextRead"] | null;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-context-update_agent_context": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentContextUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentContextRead"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "agent-context-get_agent_contexts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentContextListInput"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentContextListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-get_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ShareResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-create_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ShareResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-revoke_share_link": {
        parameters: {
            query?: never;
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    "share-chat-get_shared_conversation": {
        parameters: {
            query?: {
                limit?: number;
            };
            header?: never;
            path: {
                share_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MessageHistoryPublic"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
}
export enum AccountEnvironement {
    production = "production",
    staging = "staging",
    development = "development"
}
export enum AgentType {
    conversation_agent = "conversation_agent",
    autonomous_agent = "autonomous_agent"
}
export enum AlertSeverity {
    CRITICAL = "CRITICAL",
    HIGH = "HIGH",
    MEDIUM = "MEDIUM",
    LOW = "LOW",
    INFO = "INFO"
}
export enum AlertStatus {
    OPEN = "OPEN",
    ACKNOWLEDGED = "ACKNOWLEDGED",
    RESOLVED = "RESOLVED",
    CLOSED = "CLOSED"
}
export enum AsyncTaskStatus {
    PENDING = "PENDING",
    PROGRESS = "PROGRESS",
    SUCCESS = "SUCCESS",
    FAILURE = "FAILURE"
}
export enum ChartType {
    line = "line",
    bar = "bar",
    pie = "pie",
    doughnut = "doughnut",
    area = "area",
    scatter = "scatter",
    radar = "radar",
    step_area = "step_area"
}
export enum CloudProvider {
    AWS = "AWS"
}
export enum ConnectorType {
    bedrock_kb = "bedrock_kb",
    open_api = "open_api"
}
export enum DocumentType {
    url = "url",
    file = "file"
}
export enum FeedbackType {
    good = "good",
    bad = "bad"
}
export enum FileType {
    file = "file",
    directory = "directory"
}
export enum KBAccessLevel {
    private = "private",
    shared = "shared"
}
export enum KBUsageMode {
    manual = "manual",
    agent_requested = "agent_requested",
    always = "always"
}
export enum MCPServerStatus {
    connected = "connected",
    error = "error"
}
export enum MCPServerTransport {
    streamable_http = "streamable_http",
    sse = "sse"
}
export enum MessageActionType {
    none = "none",
    recommendation = "recommendation"
}
export enum MessageDisplayComponentType {
    table = "table",
    chart = "chart"
}
export enum MetricType {
    usage = "usage",
    performance = "performance",
    cost = "cost"
}
export enum NodeType {
    start = "start",
    tool = "tool",
    human_in_loop = "human_in_loop",
    end = "end",
    output = "output"
}
export enum NotificationStatus {
    unread = "unread",
    read = "read",
    archived = "archived"
}
export enum NotificationType {
    info = "info",
    warning = "warning",
    error = "error",
    interrupt = "interrupt"
}
export enum RecommendationStatus {
    pending = "pending",
    implemented = "implemented",
    ignored = "ignored",
    in_progress = "in_progress"
}
export enum RecommendationType {
    instance_rightsizing = "instance_rightsizing",
    autoscaling_optimization = "autoscaling_optimization",
    auto_start_stop_optimization = "auto_start_stop_optimization",
    volume_optimization = "volume_optimization",
    snapshot_cleanup = "snapshot_cleanup",
    reserved_instance_recommendation = "reserved_instance_recommendation",
    savings_plan_recommendation = "savings_plan_recommendation",
    spot_instance_usage = "spot_instance_usage",
    idle_resource_cleanup = "idle_resource_cleanup",
    unused_eip_cleanup = "unused_eip_cleanup",
    orphaned_snapshot_cleanup = "orphaned_snapshot_cleanup",
    underutilized_ebs_cleanup = "underutilized_ebs_cleanup",
    serverless_migration = "serverless_migration",
    container_adoption = "container_adoption",
    multi_az_optimization = "multi_az_optimization",
    data_transfer_optimization = "data_transfer_optimization",
    cloudfront_optimization = "cloudfront_optimization",
    nat_gateway_optimization = "nat_gateway_optimization",
    rds_optimization = "rds_optimization",
    redshift_optimization = "redshift_optimization",
    dynamodb_optimization = "dynamodb_optimization",
    s3_storage_class_optimization = "s3_storage_class_optimization",
    lambda_optimization = "lambda_optimization",
    tagging_improvement = "tagging_improvement",
    cost_allocation_improvement = "cost_allocation_improvement",
    cost_anomaly_detection = "cost_anomaly_detection",
    budget_alert_setup = "budget_alert_setup",
    cost_explorer_usage = "cost_explorer_usage",
    modernize_legacy_services = "modernize_legacy_services",
    migrate_to_graviton = "migrate_to_graviton",
    compliance_optimization = "compliance_optimization",
    governance_improvement = "governance_improvement",
    cross_region_optimization = "cross_region_optimization",
    cross_account_optimization = "cross_account_optimization",
    predictive_scaling = "predictive_scaling",
    ai_driven_optimization = "ai_driven_optimization",
    quantum_computing_readiness = "quantum_computing_readiness",
    carbon_footprint_reduction = "carbon_footprint_reduction",
    renewable_energy_usage = "renewable_energy_usage",
    marketplace_alternative = "marketplace_alternative",
    third_party_tool_recommendation = "third_party_tool_recommendation",
    custom_optimization = "custom_optimization",
    other = "other",
    ec2_fleet_optimization = "ec2_fleet_optimization",
    spot_fleet_optimization = "spot_fleet_optimization",
    graviton_migration = "graviton_migration",
    predictive_scaling_optimization = "predictive_scaling_optimization",
    instance_connect_endpoint = "instance_connect_endpoint"
}
export enum ResourceStatus {
    stopped = "stopped",
    starting = "starting",
    running = "running",
    found = "found",
    deleted = "deleted"
}
export enum ResourceType {
    EC2 = "EC2",
    LAMBDA = "LAMBDA",
    ECS = "ECS",
    EKS = "EKS",
    BATCH = "BATCH",
    EC2_AUTO_SCALING = "EC2_AUTO_SCALING",
    ELASTIC_BEANSTALK = "ELASTIC_BEANSTALK",
    APP_RUNNER = "APP_RUNNER",
    RDS = "RDS",
    DYNAMODB = "DYNAMODB",
    ELASTICACHE = "ELASTICACHE",
    NEPTUNE = "NEPTUNE",
    DOCUMENTDB = "DOCUMENTDB",
    OPENSEARCH = "OPENSEARCH",
    REDSHIFT = "REDSHIFT",
    S3 = "S3",
    EBS = "EBS",
    EFS = "EFS",
    BACKUP = "BACKUP",
    VPC = "VPC",
    ELB = "ELB",
    CLOUDFORMATION = "CLOUDFORMATION",
    CLOUDWATCH = "CLOUDWATCH",
    SQS = "SQS",
    SNS = "SNS"
}
export enum RetrieverConfigOverrideSearchType {
    HYBRID = "HYBRID",
    SEMANTIC = "SEMANTIC"
}
export enum RunModeEnum {
    autonomous = "autonomous",
    agent = "agent"
}
export enum TaskCategoryEnum {
    COST_OPTIMIZE = "COST_OPTIMIZE",
    OPERATIONAL = "OPERATIONAL",
    SCALABILITY = "SCALABILITY",
    SECURITY = "SECURITY",
    OPERATIONAL_EFFICIENCY = "OPERATIONAL_EFFICIENCY",
    OTHER = "OTHER"
}
export enum TaskCouldEnum {
    AWS = "AWS",
    AZURE = "AZURE",
    GCP = "GCP",
    ALL = "ALL"
}
export enum TaskExecutionStatus {
    running = "running",
    succeeded = "succeeded",
    failed = "failed",
    cancelled = "cancelled",
    required_approval = "required_approval"
}
export enum TaskPriority {
    Value0 = 0,
    Value1 = 1,
    Value2 = 2,
    Value3 = 3
}
export enum TaskScheduledStatus {
    pending = "pending",
    scheduled = "scheduled"
}
export enum TaskServiceEnum {
    ALL = "ALL",
    OTHER = "OTHER",
    COMPUTE = "COMPUTE",
    STORAGE = "STORAGE",
    SERVERLESS = "SERVERLESS",
    DATABASE = "DATABASE",
    NETWORK = "NETWORK",
    MESSAGING = "MESSAGING",
    MANAGEMENT = "MANAGEMENT",
    BILLING = "BILLING",
    CROSS_SERVICE = "CROSS_SERVICE",
    MONITORING = "MONITORING",
    STREAMING = "STREAMING",
    SECURITY = "SECURITY"
}
export enum UploadStatus {
    pending = "pending",
    in_progress = "in_progress",
    completed = "completed",
    failed = "failed"
}
export enum WorkflowStatus {
    created = "created",
    unvalidated = "unvalidated",
    running = "running",
    pending = "pending",
    completed = "completed",
    error = "error"
}
