import { urlConfig } from '@/config/url.config';
import { isBrowser } from '@/utils/is-browser';
import { signOut } from 'next-auth/react';
import makeFetch from 'openapi-fetch';
import { toast } from 'sonner';

import { paths } from './gens';
import { getAccessToken } from './get-access-token';

// Create fetch instance with the base URL from config
export const api = makeFetch<paths>({
  baseUrl: urlConfig.apiUrl,

  fetch: async (request) => {
    // Get access token from NextAuth session
    const accessToken = await getAccessToken();

    if (accessToken) {
      request.headers.set('Authorization', `Bearer ${accessToken}`);
    }

    let response: Response;

    if ('next' in request) {
      response = await fetch(request);
    } else {
      response = await fetch(request, {
        next: {
          revalidate: 60,
        },
      });
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 401) {
        // Handle 401 unauthorized by signing out through NextAuth
        if (isBrowser()) {
          await signOut({ redirect: true, callbackUrl: '/auth/sign-in' });
        } else {
          const { redirect } = await import('next/navigation');

          // redirect(pathsConfig.app.signOut);
          redirect('/sign-out');
        }
      }

      if (isBrowser()) {
        toast.error(errorData.message);
      }

      throw new ApiError('API request failed', response.status, errorData);
    }

    return response;
  },
});

export async function fetchData<T>(fn: Promise<{ data?: T }>): Promise<T> {
  const { data } = await fn;
  return data!;
}

/**
 * Interpolates path parameters into a URL path.
 *
 * @param path - The URL path with placeholders for parameters
 * @param params - An object containing parameter values to replace in the path
 * @returns The interpolated URL path with parameter values replaced
 */
export function interpolatePath(
  path: keyof paths,
  params: Record<string, string | number>,
) {
  return path.replace(/{(\w+)}/g, (_, key) => {
    const value = params[key];
    if (!value) throw new Error(`Missing param: ${key}`);
    return encodeURIComponent(value.toString());
  });
}

export class ApiError extends Error {
  status: number;
  data: {
    message: string;
    statusCode: number;
  };

  constructor(
    message: string,
    status: number,
    data: {
      message: string;
      statusCode: number;
    },
  ) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
    // This is necessary for instanceof to work correctly
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}
