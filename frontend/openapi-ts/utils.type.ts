// export type PathsRequestDto<T extends keyof paths> =
//   paths[T]['post']['requestBody']['content']['application/json'];
import { paths } from './gens';

export type PathsRequestBodyDto<T extends keyof paths> = T extends keyof paths
  ? 'post' extends keyof paths[T]
    ? 'requestBody' extends keyof paths[T]['post']
      ? 'content' extends keyof paths[T]['post']['requestBody']
        ? 'application/json' extends keyof paths[T]['post']['requestBody']['content']
          ? paths[T]['post']['requestBody']['content']['application/json']
          : never
        : never
      : never
    : never
  : never;

export type PathsUpdateRequestBodyDto<T extends keyof paths> =
  T extends keyof paths
    ? 'put' extends keyof paths[T]
      ? 'requestBody' extends keyof paths[T]['put']
        ? 'content' extends keyof paths[T]['put']['requestBody']
          ? 'application/json' extends keyof paths[T]['put']['requestBody']['content']
            ? paths[T]['put']['requestBody']['content']['application/json']
            : never
          : never
        : never
      : never
    : never;

export type PathsRequestQueryDto<T extends keyof paths> = T extends keyof paths
  ? 'get' extends keyof paths[T]
    ? 'parameters' extends keyof paths[T]['get']
      ? 'query' extends keyof paths[T]['get']['parameters']
        ? paths[T]['get']['parameters']['query']
        : never
      : never
    : never
  : never;
