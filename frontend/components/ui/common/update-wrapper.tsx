import { PropsWithChildren } from 'react';

import { cn } from '@/lib/utils';
import { Loader2Icon } from 'lucide-react';

import { If } from './if';

/**
 * Props for the UpdateWrapper component
 *
 * @typedef {Object} UpdateWrapperProps
 * @property {boolean} isPending - Whether the wrapped content is in a pending/loading state
 */
type Props = PropsWithChildren<{
  isPending?: boolean;
  className?: string;
}>;

/**
 * A component that shows a loading indicator alongside content when in a pending state
 *
 * This component wraps content with a spinner that appears when the isPending
 * flag is true. Useful for indicating loading states during updates, form submissions,
 * or any async operation without replacing the entire content.
 *
 * @example
 * ```tsx
 * <UpdateWrapper isPending={isSubmitting}>
 *   <Button type="submit">Save Changes</Button>
 * </UpdateWrapper>
 * ```
 *
 * @example
 * ```tsx
 * <UpdateWrapper isPending={isLoading}>
 *   <UserProfile user={user} />
 * </UpdateWrapper>
 * ```
 */
export const UpdateWrapper = ({ children, isPending, className }: Props) => {
  return (
    <div className={cn('flex items-center justify-center gap-2', className)}>
      <If condition={isPending}>
        <Loader2Icon className="size-4 animate-spin" />
      </If>
      {children}
    </div>
  );
};
