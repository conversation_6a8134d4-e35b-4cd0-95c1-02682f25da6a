'use client';

import { useMemo } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { buttonVariants } from '@/components/ui/button';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import { useToggle } from 'usehooks-ts';

/**
 * Props for the DeleteConfirmAlert component
 *
 * @typedef {Object} DeleteConfirmAlertProps
 * @property {React.ReactNode | ((toggle: () => void) => React.ReactNode)} children - React children or a render prop function receiving the toggle function
 * @property {string} title - The title displayed in the alert dialog
 * @property {string} [description] - Optional description text displayed below the title
 * @property {(toggle: () => void) => void} onConfirm - Callback function called when the user confirms the delete action
 * @property {boolean} loading - Whether the component is in a loading state
 */
type Props = {
  children: React.ReactNode | ((toggle: () => void) => React.ReactNode);
  title: string;
  description?: string;
  onConfirm: (toggle: () => void) => void;
  loading: boolean;
};

/**
 * A reusable alert dialog component for confirming delete actions
 *
 * This component displays a confirmation dialog before performing a delete action.
 * It shows a loading spinner during the deletion process and can be triggered
 * either directly or via a function that receives the toggle handler.
 *
 * @example
 * ```tsx
 * <DeleteConfirmAlert
 *   title="Delete Item"
 *   description="Are you sure you want to delete this item?"
 *   onConfirm={() => handleDelete(itemId)}
 *   loading={isDeleting}
 * >
 *   <Button variant="destructive">Delete</Button>
 * </DeleteConfirmAlert>
 * ```
 *
 * @example
 * ```tsx
 * <DeleteConfirmAlert
 *   title="Delete Item"
 *   onConfirm={() => handleDelete(itemId)}
 *   loading={isDeleting}
 * >
 *   {(toggle) => (
 *     <IconButton onClick={toggle} icon={<TrashIcon />} />
 *   )}
 * </DeleteConfirmAlert>
 */
export const DeleteConfirmAlert = ({
  children,
  title,
  description,
  onConfirm,
  loading,
}: Props) => {
  // Use the useToggle hook to manage the open/closed state of the dialog
  const [open, toggle] = useToggle(false);

  // Handle both direct children and render prop patterns
  const child = useMemo(
    () => (typeof children === 'function' ? children(toggle) : children),
    [children, toggle],
  );

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>{child}</AlertDialogTrigger>

      <AlertDialogContent onEscapeKeyDown={(e) => e.preventDefault()}>
        <SpinnerContainer loading={loading}>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>
              {description} {'This action cannot be undone'}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>

            <AlertDialogAction
              type={'button'}
              onClick={() => onConfirm(toggle)}
              className={buttonVariants({ variant: 'destructive' })}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </SpinnerContainer>
      </AlertDialogContent>
    </AlertDialog>
  );
};
