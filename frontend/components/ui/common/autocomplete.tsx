'use client';

import {
  FC,
  ReactNode,
  createElement,
  useCallback,
  useMemo,
  useState,
} from 'react';

import { Badge } from '@/components/ui/badge';
import { Button, ButtonProps } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { FormControl } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { firstLetterUppercase } from '@/utils/string';
import { Check, ChevronsUpDown, X } from 'lucide-react';

import { If } from './if';

type AutocompleteOption = {
  label: ReactNode;
  value: string;
  icon?: FC<{ className?: string }>;
};

type BaseAutocompleteProps = {
  options: AutocompleteOption[] | undefined;
  name: string;
  CommandProps?: React.ComponentPropsWithRef<typeof Command>;
  CommandInputProps?: React.ComponentPropsWithRef<typeof CommandInput>;
  className?: string;
  PopoverContentProps?: React.ComponentPropsWithRef<typeof PopoverContent>;
  placeholder?: string;
  size?: ButtonProps['size'];
  clearable?: boolean;
  maxDisplayLength?: number;
};

export type SingleAutocompleteProps = BaseAutocompleteProps & {
  mode?: 'single';
  value: string | undefined | null;
  onChange: (value: string | undefined | null) => void;
};

type MultipleAutocompleteProps = BaseAutocompleteProps & {
  mode: 'multiple';
  value: string[] | undefined | null;
  onChange: (value: string[] | undefined | null) => void;
};

type AutocompleteProps = SingleAutocompleteProps | MultipleAutocompleteProps;

export const Autocomplete = (props: AutocompleteProps) => {
  const {
    options = [],
    name: nameOriginal,
    CommandProps,
    CommandInputProps,
    className,
    PopoverContentProps,
    placeholder,
    size,
    clearable = true,
    maxDisplayLength = 2,
  } = props;

  const name = convertToNormalText(nameOriginal);

  const [open, setOpen] = useState(false);
  const searchSubject = placeholder ? placeholder.toLowerCase() : name;

  const isMultipleMode = props.mode === 'multiple';

  // Handle values based on mode
  const singleValue = isMultipleMode ? null : props.value;
  const multipleValues = useMemo(() => {
    return isMultipleMode
      ? Array.isArray(props.value)
        ? props.value
        : []
      : [];
  }, [isMultipleMode, props.value]);

  // Selection handlers
  const handleSingleSelect = useCallback(
    (value: string) => {
      if (!isMultipleMode) {
        props.onChange(value);
        setOpen(false);
      }
    },
    [isMultipleMode, props],
  );

  const handleMultipleSelect = useCallback(
    (value: string) => {
      if (isMultipleMode) {
        const selectedValues = multipleValues;
        if (selectedValues.includes(value)) {
          props.onChange(selectedValues.filter((v) => v !== value));
        } else {
          props.onChange([...selectedValues, value]);
        }
      }
    },
    [isMultipleMode, multipleValues, props],
  );

  const handleClear = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (isMultipleMode) {
        props.onChange([]);
      } else {
        props.onChange(null);
      }
    },
    [isMultipleMode, props],
  );

  const handleRemoveItem = useCallback(
    (value: string, e?: React.MouseEvent) => {
      if (e) {
        e.stopPropagation();
      }
      if (isMultipleMode) {
        props.onChange(multipleValues.filter((v) => v !== value));
      }
    },
    [isMultipleMode, multipleValues, props],
  );

  // Render button content based on mode
  const renderButtonContent = () => {
    if (isMultipleMode) {
      return (
        <>
          <div className="flex flex-wrap gap-1">
            <If
              condition={multipleValues.length > 0}
              fallback={<span>{placeholder ?? `Select ${name}`}</span>}
            >
              {firstLetterUppercase(name)}
              <If
                condition={multipleValues.length < maxDisplayLength}
                fallback={
                  <Badge variant="secondary">
                    {multipleValues.length} selected
                  </Badge>
                }
              >
                {multipleValues.map((selectedValue) => {
                  const option = options?.find(
                    (opt) => opt.value === selectedValue,
                  );
                  return (
                    <Badge key={selectedValue} variant="secondary">
                      {option?.label}
                      {clearable && (
                        <X
                          className="ml-1 h-3 w-3 cursor-pointer"
                          onClick={(e) => handleRemoveItem(selectedValue, e)}
                        />
                      )}
                    </Badge>
                  );
                })}
              </If>
            </If>
          </div>
          <div className="ml-2 flex shrink-0 items-center gap-2">
            {multipleValues.length > 0 && clearable && (
              <X
                className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100"
                onClick={handleClear}
              />
            )}
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </>
      );
    }

    return (
      <>
        {singleValue
          ? options?.find((option) => option.value === singleValue)?.label
          : (placeholder ?? `Select ${name}`)}
        <div className="ml-2 flex items-center gap-2">
          {singleValue && clearable && (
            <X
              className="h-4 w-4 shrink-0 opacity-50 hover:opacity-100"
              onClick={handleClear}
            />
          )}
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </div>
      </>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            type="button"
            variant="outline"
            size={size}
            role="combobox"
            className={cn(
              'min-w-fit justify-between bg-transparent',
              (isMultipleMode ? multipleValues.length === 0 : !singleValue) &&
                'text-muted-foreground',
              isMultipleMode && '',
              className,
            )}
          >
            {renderButtonContent()}
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent
        {...PopoverContentProps}
        className={cn(
          'mx-4 w-auto p-0 md:mx-0 md:w-[200px]',
          PopoverContentProps?.className,
        )}
      >
        <Command {...CommandProps}>
          <CommandInput
            placeholder={`Search ${searchSubject}...`}
            {...CommandInputProps}
          />
          <CommandList>
            <CommandEmpty>No {searchSubject} found.</CommandEmpty>
            <CommandGroup>
              {options?.map((option) => (
                <CommandItem
                  value={option.value}
                  key={option.value}
                  onSelect={() => {
                    if (isMultipleMode) {
                      handleMultipleSelect(option.value);
                    } else {
                      handleSingleSelect(option.value);
                    }
                  }}
                >
                  <If condition={option.icon} fallback={option.label}>
                    {(icon) => (
                      <div className="flex items-center gap-2">
                        {createElement(icon, {
                          className: 'size-4',
                        })}
                        {option.label}
                      </div>
                    )}
                  </If>
                  <Check
                    className={cn(
                      'ml-auto',
                      isMultipleMode
                        ? multipleValues.includes(option.value)
                          ? 'opacity-100'
                          : 'opacity-0'
                        : option.value === singleValue
                          ? 'opacity-100'
                          : 'opacity-0',
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export const convertToNormalText = (text: string): string => {
  return (
    text
      // Convert camelCase to spaces: insert space before capital letters
      .replace(/([a-z0-9])([A-Z])/g, '$1 $2')
      // Convert snake_case to spaces
      .replace(/_/g, ' ')
      // Lowercase everything (optional, depending on style)
      .toLowerCase()
      .trim()
  );
};
