'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Button, ButtonProps } from '@/components/ui/button';
import { <PERSON>rollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  Table as ReactTable,
  Row,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';
import { range } from 'lodash';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

import { SpinnerContainer } from '../spinner-container';

interface ReactTableProps<T extends object> {
  data: T[];
  columns: ColumnDef<T>[];
  renderSubComponent?: (props: { row: Row<T> }) => React.ReactElement;
  pageIndex?: number;
  pageSize?: number;
  pageCount?: number;
  onPaginationChange?: (pagination: PaginationState) => void;
  manualPagination?: boolean;
  manualSorting?: boolean;
  tableProps?: React.ComponentProps<typeof Table> &
    Record<`data-${string}`, string>;
  isRefetching?: boolean;
  itemCount: number;
}

export function NewDataTable<T extends object>({
  data,
  columns,
  pageIndex,
  pageSize,
  pageCount,
  onPaginationChange,
  tableProps,
  manualPagination = true,
  manualSorting = false,
  isRefetching = false,
  itemCount,
}: ReactTableProps<T>) {
  const defaultPagination = useMemo(
    () => ({
      pageIndex: pageIndex ?? 0,
      pageSize: pageSize ?? 10,
    }),
    [pageIndex, pageSize],
  );

  const [pagination, setPagination] = useState<PaginationState>(
    () => defaultPagination,
  );

  useEffect(() => {
    if (defaultPagination.pageIndex === pagination.pageIndex) return;
    setPagination(defaultPagination);
  }, [defaultPagination]);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const navigateToPage = useNavigateToNewPage();

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination,
    manualSorting,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    pageCount,
    state: {
      pagination,
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        setPagination((prevState) => {
          const nextState = updater(prevState);

          if (onPaginationChange) {
            onPaginationChange(nextState);
          } else {
            navigateToPage(nextState.pageIndex);
          }

          return nextState;
        });
      } else {
        setPagination(updater);

        if (onPaginationChange) {
          onPaginationChange(updater);
        } else {
          navigateToPage(updater.pageIndex);
        }
      }
    },
  });

  return (
    <SpinnerContainer
      loading={isRefetching}
      className={'flex h-full flex-col rounded-lg border'}
    >
      <ScrollArea>
        <Table {...tableProps}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const { meta } = header.column.columnDef;

                  return (
                    <TableHead
                      colSpan={header.colSpan}
                      style={{
                        width: header.column.getSize(),
                      }}
                      key={header.id}
                      className={cn(meta?.className, meta?.header?.className)}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => {
                    const { meta } = cell.column.columnDef;

                    return (
                      <TableCell
                        key={cell.id}
                        className={cn(meta?.className, meta?.cell?.className)}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
      <Pagination table={table} itemCount={itemCount} />
    </SpinnerContainer>
  );
}

function Pagination<T>({
  table,
  itemCount,
}: React.PropsWithChildren<{
  table: ReactTable<T>;
  itemCount: number;
}>) {
  if (!table.getPageCount()) return null;

  const { getState, getPageCount, setPagination } = table;

  const page = getState().pagination.pageIndex + 1;
  const pageCount = getPageCount();

  const items = generatePages(page, pageCount, 1);

  const defaultVariant: ButtonProps['variant'] = 'ghost';

  return (
    <div className="relative flex items-center justify-center space-x-4 py-2">
      <div className="absolute left-4 flex h-full items-center">
        <p className="text-muted-foreground text-sm">{itemCount} results</p>
      </div>
      <div className="flex items-center justify-end space-x-1">
        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => table.setPageIndex(0)}
          disabled={!table.getCanPreviousPage()}
        >
          <ChevronsLeft className="size-4" />
        </Button>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={table.previousPage}
          disabled={!table.getCanPreviousPage()}
        >
          <ChevronLeft className="size-4" />
        </Button>

        <p className="mx-2 text-sm font-medium md:hidden">
          {page} / {pageCount}
        </p>

        <div className="hidden items-center gap-1 md:flex">
          {items.map((pageNumber, index) => {
            if (typeof pageNumber === 'string') {
              return (
                <Button
                  key={`${pageNumber}-${index}`}
                  size="sm"
                  variant={'ghost'}
                  className={cn(
                    'aspect-square rounded-xs',
                    'pointer-events-none',
                  )}
                >
                  {pageNumber}
                </Button>
              );
            }

            return (
              <Button
                key={pageNumber}
                variant={defaultVariant}
                className={cn('size-9 rounded-lg', {
                  'border-primary pointer-events-none border':
                    pageNumber === page,
                })}
                onClick={() => {
                  setPagination((old) => ({
                    ...old,
                    pageIndex: pageNumber - 1,
                  }));
                }}
              >
                {pageNumber}
              </Button>
            );
          })}
        </div>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={table.nextPage}
          disabled={!table.getCanNextPage()}
        >
          <ChevronRight className="size-4" />
        </Button>

        <Button
          size={'icon'}
          variant={defaultVariant}
          onClick={() => table.setPageIndex(table.getPageCount() - 1)}
          disabled={!table.getCanNextPage()}
        >
          <ChevronsRight className="size-4" />
        </Button>
      </div>
    </div>
  );
}

/**
 * Navigates to a new page using the provided page index and optional page parameter.
 */
function useNavigateToNewPage(
  props: { pageParam?: string } = {
    pageParam: 'page',
  },
) {
  const router = useRouter();
  const param = props.pageParam ?? 'page';

  return useCallback(
    (pageIndex: number) => {
      const url = new URL(window.location.href);
      url.searchParams.set(param, String(pageIndex + 1));
      // Use replace to avoid adding to browser history and disable scroll
      router.replace(url.pathname + url.search, { scroll: false });
    },
    [param, router],
  );
}

const ELLIPSES = '...';

/**
 * Generates a list of page numbers for pagination, including ellipses (`...`) if necessary.
 * @param current - The current active page.
 * @param total - The total number of pages.
 * @param siblings - The number of pages to display on each side of the current page.
 * @returns An array of page numbers, possibly including `...` as placeholders.
 */
const generatePages = (current: number, total: number, siblings: number) => {
  const totalNumbers = siblings * 2 + 3;
  const totalBlocks = totalNumbers + 2;

  if (total <= totalBlocks) {
    return range(1, total + 1);
  }

  const startPage = Math.max(current - siblings, 1);
  const endPage = Math.min(current + siblings, total);

  const hasLeftEllipsis = startPage > 2;
  const hasRightEllipsis = endPage < total - 1;

  // Use concatenation and spread operator instead of push
  const leftPages = hasLeftEllipsis ? [1, ELLIPSES] : range(1, startPage);

  const middlePages = range(startPage, endPage + 1);

  const rightPages = hasRightEllipsis
    ? [ELLIPSES, total]
    : range(endPage + 1, total + 1);

  // Combine all sections
  return [...leftPages, ...middlePages, ...rightPages];
};
