/* eslint-disable @typescript-eslint/no-unused-vars */
import '@tanstack/react-table';

declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends RowData, TValue> {
    className?: string;
    cell?: {
      className: string;
    };
    header?: {
      className: string;
    };
  }

  interface HeaderContext<TData extends RowData, TValue> {
    isColumnVisibilityDropdown?: boolean;
  }
}
