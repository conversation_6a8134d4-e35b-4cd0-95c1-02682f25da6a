import { PropsWithChildren } from 'react';

import { cn } from '@/lib/utils';

import { If } from './common/if';
import { Spinner } from './spinner';

type Props = PropsWithChildren<{
  loading: boolean;
  className?: string;
}>;

export const SpinnerContainer = ({ loading, children, className }: Props) => {
  return (
    <div className={cn('relative inline-block w-full', className)}>
      <If condition={loading}>
        <div className="bg-foreground/5 absolute inset-0 z-10 flex items-center justify-center">
          <Spinner />
        </div>
      </If>
      {children}
    </div>
  );
};
