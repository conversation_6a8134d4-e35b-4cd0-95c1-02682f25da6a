"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Folder, File, ChevronRight, ChevronDown, RefreshCw, AlertCircle } from 'lucide-react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import { GripVertical } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FilePreview } from './file-preview';
import { useFileExplorer, FileNode } from '@/hooks/use-file-explorer';

interface FileExplorerProps {
  isUserActivated: boolean;
  currentPath: string;
  onPathChange: (path: string) => void;
  conversationId?: string;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({
  isUserActivated,
  currentPath,
  onPathChange,
  conversationId
}) => {
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set(['/workspace']));
  const [fileSystem, setFileSystem] = useState<FileNode[]>([]);
  const [fileError, setFileError] = useState<string | null>(null);
  const { getFiles, getFileContent, isLoading, error } = useFileExplorer();

  const fetchFiles = useCallback(async () => {
    setFileError(null);
    const files = await getFiles();
    setFileSystem(files);
  }, [getFiles]);

  // Fetch files when component mounts or conversation changes
  useEffect(() => {
    if (isUserActivated) {
      fetchFiles();
    }
  }, [isUserActivated, conversationId, fetchFiles]);

  // Recursive component for rendering file tree
  const FileTreeNode: React.FC<{
    node: FileNode;
    level: number;
  }> = ({ node, level }) => {
    const isExpanded = expandedPaths.has(node.path);
    const hasChildren = node.children && node.children.length > 0;

    const handleToggle = () => {
      if (hasChildren) {
        setExpandedPaths(prev => {
          const newSet = new Set(prev);
          if (newSet.has(node.path)) {
            newSet.delete(node.path);
          } else {
            newSet.add(node.path);
          }
          return newSet;
        });
      }
    };

    const handleClick = async () => {
      if (node.type === 'file') {
        try {
          setFileError(null);
          const content = await getFileContent(node.path);
          setSelectedFile({ ...node, content });
        } catch (err) {
          setFileError(err instanceof Error ? err.message : 'Failed to load file');
          setSelectedFile({ ...node, content: undefined });
        }
      } else {
        handleToggle();
        onPathChange(node.path);
      }
    };

    return (
      <div>
        <div
          className={`flex items-center gap-1 py-1 px-2 hover:bg-accent/50 rounded-sm cursor-pointer text-sm transition-colors ${
            selectedFile?.path === node.path ? 'bg-accent' : ''
          }`}
          style={{ paddingLeft: `${level * 12}px` }}
          onClick={handleClick}
        >
          {node.type === 'directory' && (
            isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
          )}
          {node.type === 'directory' ? (
            <Folder className="h-4 w-4 text-yellow-500" />
          ) : (
            <File className="h-4 w-4 text-blue-500" />
          )}
          <span>{node.name}</span>
        </div>
        {isExpanded && node.children && (
          <div>
            {node.children.map(child => (
              <FileTreeNode key={child.path} node={child} level={level + 1} />
            ))}
          </div>
        )}
      </div>
    );
  };

  if (!isUserActivated) {
    return (
      <div className="flex items-center justify-center h-full bg-background/50 border-b border-border">
        <div className="text-center text-muted-foreground space-y-3">
          <Folder className="h-12 w-12 mx-auto text-muted-foreground/50" />
          <div>
            <p className="text-sm font-medium">File Explorer</p>
            <p className="text-xs">Activate console to browse files</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PanelGroup direction="horizontal">
      <Panel defaultSize={30} minSize={20}>
        <div className="h-full border-r flex flex-col">
          <div className="flex items-center justify-between p-2 border-b">
            <h3 className="text-sm font-medium">File Explorer</h3>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={fetchFiles}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span className="sr-only">Refresh files</span>
            </Button>
          </div>
          <ScrollArea className="flex-1">
            <div className="p-2">
              {isLoading && fileSystem.length === 0 ? (
                <div className="p-4 text-sm text-muted-foreground">Loading...</div>
              ) : error ? (
                <Alert variant="destructive" className="mx-2">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : fileSystem.length === 0 ? (
                <div className="p-4 text-sm text-muted-foreground">No files found</div>
              ) : (
                fileSystem.map(node => (
                  <FileTreeNode key={node.path} node={node} level={0} />
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </Panel>
      <PanelResizeHandle>
        <div className="w-2 bg-border hover:bg-accent transition-colors flex items-center justify-center">
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
      </PanelResizeHandle>
      <Panel defaultSize={70} minSize={30}>
        <FilePreview file={selectedFile} isLoading={isLoading} error={fileError} />
      </Panel>
    </PanelGroup>
  );
};
