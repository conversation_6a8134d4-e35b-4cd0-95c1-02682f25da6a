"use client";

import React, { useMemo } from 'react';
import { File, FileText, AlertCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  content?: string;
}

interface FilePreviewProps {
  file: FileNode | null;
  isLoading: boolean;
  error: string | null;
}

// Format and highlight code content
const CodeFormatter: React.FC<{ content: string; language: string; fileName: string }> = ({
  content,
  language,
  fileName
}) => {
  const formattedContent = useMemo(() => {
    const lines = content.split('\n');

    return lines.map((line, index) => {
      let highlightedLine = line;

      // Basic syntax highlighting based on language
      if (language === 'javascript' || language === 'typescript') {
        // Keywords
        highlightedLine = highlightedLine.replace(
          /\b(const|let|var|function|return|if|else|for|while|class|import|export|from|interface|type|extends|implements|async|await|try|catch|finally|throw|new|this|super|static|private|public|protected|readonly)\b/g,
          '<span class="text-blue-400 font-medium">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(\/\/.*$|\/\*[\s\S]*?\*\/)/g,
          '<span class="text-gray-500 italic">$1</span>'
        );
        // Numbers
        highlightedLine = highlightedLine.replace(
          /\b(\d+\.?\d*)\b/g,
          '<span class="text-orange-400">$1</span>'
        );
      } else if (language === 'json') {
        // JSON keys
        highlightedLine = highlightedLine.replace(
          /"([^"]+)"(\s*:)/g,
          '<span class="text-blue-400">"$1"</span>$2'
        );
        // JSON strings
        highlightedLine = highlightedLine.replace(
          /:\s*"([^"]*)"/g,
          ': <span class="text-green-400">"$1"</span>'
        );
        // JSON numbers/booleans
        highlightedLine = highlightedLine.replace(
          /:\s*(true|false|\d+\.?\d*)/g,
          ': <span class="text-orange-400">$1</span>'
        );
      } else if (language === 'css') {
        // CSS properties
        highlightedLine = highlightedLine.replace(
          /([a-zA-Z-]+)(\s*:)/g,
          '<span class="text-blue-400">$1</span>$2'
        );
        // CSS values
        highlightedLine = highlightedLine.replace(
          /(:\s*)([^;{]+)(;?)/g,
          '$1<span class="text-green-400">$2</span>$3'
        );
        // CSS selectors
        highlightedLine = highlightedLine.replace(
          /^(\s*)([.#]?[a-zA-Z][a-zA-Z0-9-_]*)/g,
          '$1<span class="text-yellow-400">$2</span>'
        );
      } else if (language === 'html') {
        // HTML tags
        highlightedLine = highlightedLine.replace(
          /(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
          '$1<span class="text-red-400">$2</span><span class="text-blue-400">$3</span>$4'
        );
        // HTML attributes
        highlightedLine = highlightedLine.replace(
          /(\s+)([a-zA-Z-]+)(=)/g,
          '$1<span class="text-yellow-400">$2</span>$3'
        );
      } else if (language === 'markdown') {
        // Markdown headers
        highlightedLine = highlightedLine.replace(
          /^(#{1,6}\s+)(.+)$/g,
          '<span class="text-blue-400 font-bold">$1</span><span class="text-white font-bold">$2</span>'
        );
        // Markdown bold
        highlightedLine = highlightedLine.replace(
          /(\*\*|__)([^*_]+)(\*\*|__)/g,
          '<span class="text-yellow-400">$1</span><span class="font-bold">$2</span><span class="text-yellow-400">$3</span>'
        );
        // Markdown italic
        highlightedLine = highlightedLine.replace(
          /(\*|_)([^*_]+)(\*|_)/g,
          '<span class="text-yellow-400">$1</span><span class="italic">$2</span><span class="text-yellow-400">$3</span>'
        );
        // Markdown code
        highlightedLine = highlightedLine.replace(
          /(`+)([^`]+)(`+)/g,
          '<span class="text-gray-400">$1</span><span class="text-green-400 bg-gray-800 px-1 rounded">$2</span><span class="text-gray-400">$3</span>'
        );
        // Markdown links
        highlightedLine = highlightedLine.replace(
          /(\[)([^\]]+)(\])(\()([^)]+)(\))/g,
          '<span class="text-gray-400">$1</span><span class="text-blue-400">$2</span><span class="text-gray-400">$3$4</span><span class="text-green-400">$5</span><span class="text-gray-400">$6</span>'
        );
      }

      // Add line numbers and content
      return {
        number: index + 1,
        content: highlightedLine || ' ', // Ensure empty lines have content
        original: line
      };
    });
  }, [content, language]);

  return (
    <div className="font-mono text-sm bg-gray-900 text-gray-300">
      {formattedContent.map((line, index) => (
        <div key={index} className="flex hover:bg-gray-800/50 transition-colors">
          {/* Line Numbers */}
          <div className="flex-shrink-0 w-12 text-right pr-4 text-gray-500 select-none border-r border-gray-700 bg-gray-800/50">
            {line.number}
          </div>
          {/* Code Content */}
          <div
            className="flex-1 pl-4 pr-2 py-0.5 min-h-[1.25rem] whitespace-pre"
            dangerouslySetInnerHTML={{ __html: line.content }}
          />
        </div>
      ))}
    </div>
  );
};

export const FilePreview: React.FC<FilePreviewProps> = ({ file, isLoading, error }) => {
  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'css':
      case 'scss':
      case 'sass':
        return 'css';
      case 'html':
      case 'htm':
        return 'html';
      case 'json':
        return 'json';
      case 'md':
      case 'markdown':
        return 'markdown';
      case 'py':
        return 'python';
      case 'sh':
      case 'bash':
        return 'bash';
      case 'xml':
        return 'xml';
      case 'yaml':
      case 'yml':
        return 'yaml';
      default:
        return 'text';
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="flex items-center gap-2 text-gray-400">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          Loading...
        </div>
      </div>
    );
  }

  if (!file) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="text-center text-gray-400 space-y-3">
          <FileText className="h-12 w-12 mx-auto opacity-50" />
          <div>
            <p className="text-sm">Select a file to preview</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900 p-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="ml-2">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (file.type === 'directory') {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="text-center text-gray-500 space-y-3">
          <File className="h-16 w-16 mx-auto opacity-50" />
          <div>
            <p className="text-lg font-medium">Folder selected</p>
            <p className="text-sm">Select a file to view its content</p>
          </div>
        </div>
      </div>
    );
  }

  const language = getLanguageFromExtension(file.name);
  const isCodeFile = ['javascript', 'typescript', 'json', 'css', 'html', 'markdown', 'python', 'bash', 'xml', 'yaml'].includes(language);

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* File Header */}
      <div className="flex items-center gap-3 px-4 py-3 border-b border-gray-700 bg-gray-800">
        <File className="h-4 w-4 text-gray-400" />
        <span className="text-sm font-medium text-gray-200">{file.name}</span>
        <div className="ml-auto flex items-center gap-2">
          <span className="text-xs px-2 py-1 bg-blue-600/20 text-blue-400 rounded-full">
            {language}
          </span>
          {file.content && (
            <span className="text-xs text-gray-500">
              {file.content.split('\n').length} lines
            </span>
          )}
        </div>
      </div>

      {/* File Content */}
      <ScrollArea className="flex-1">
        {file.content ? (
          isCodeFile ? (
            <CodeFormatter
              content={file.content}
              language={language}
              fileName={file.name}
            />
          ) : (
            <div className="p-4 bg-gray-900">
              <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono leading-relaxed">
                {file.content}
              </pre>
            </div>
          )
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-900">
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No content available</p>
            </div>
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
