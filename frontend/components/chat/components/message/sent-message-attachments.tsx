'use client';

import React, { useState, useEffect } from 'react';
import { File, Image, FileText, Download, Eye } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';
import { MessageAttachment } from '@/components/chat/types';
import { attachmentUrlCache } from '@/lib/attachment-url-cache';
import { SimplePDFViewer } from '@/app/(dashboard)/kb/components/SimplePDFViewer';
import { AttachmentMetadataResponse } from '@/client';

interface SentMessageAttachmentsProps {
  attachmentIds: string[];
  fetchAttachmentMetadata: (attachmentId: string) => Promise<AttachmentMetadataResponse>;
  showPreview?: boolean;
}

interface SentAttachmentItemProps {
  attachmentId: string;
  fetchAttachmentMetadata: (attachmentId: string) => Promise<AttachmentMetadataResponse>;
  showPreview?: boolean;
}

// Get file type category
function getFileCategory(mimeType: string): 'image' | 'pdf' | 'text' | 'document' | 'other' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.startsWith('text/') || mimeType === 'application/json') return 'text';
  if (mimeType.includes('word') || mimeType.includes('sheet')) return 'document';
  return 'other';
}

// Get file icon based on type
function getFileIcon(mimeType: string) {
  const category = getFileCategory(mimeType);

  switch (category) {
    case 'image':
      return <Image className="h-6 w-6 text-muted-foreground" />;
    case 'pdf':
    case 'document':
      return <FileText className="h-6 w-6 text-muted-foreground" />;
    case 'text':
      return <FileText className="h-6 w-6 text-muted-foreground" />;
    default:
      return <File className="h-6 w-6 text-muted-foreground" />;
  }
}



function SentAttachmentItem({ attachmentId, fetchAttachmentMetadata, showPreview = true }: SentAttachmentItemProps) {
  const [attachment, setAttachment] = useState<MessageAttachment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(false);

  // Load attachment metadata
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        setIsLoading(true);
        const metadata = await fetchAttachmentMetadata(attachmentId);
        
        // Transform to MessageAttachment format
        const messageAttachment: MessageAttachment = {
          id: metadata.id,
          filename: metadata.filename,
          original_filename: metadata.original_filename,
          file_type: metadata.file_type,
          file_size: metadata.file_size,
          storage_key: metadata.storage_key,
          created_at: metadata.created_at,
        };
        
        setAttachment(messageAttachment);
      } catch (error) {
        console.error('Failed to load attachment metadata:', error);
        toast({
          title: 'Error',
          description: 'Failed to load attachment information.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMetadata();
  }, [attachmentId, fetchAttachmentMetadata]);

  const fileCategory = attachment ? getFileCategory(attachment.file_type) : 'other';
  const canPreview = fileCategory === 'image' || fileCategory === 'text' || fileCategory === 'pdf';

  // Load image thumbnail for display
  useEffect(() => {
    if (attachment && fileCategory === 'image' && !thumbnailUrl && !isLoadingThumbnail) {
      // For images, get the download URL to display as thumbnail
      const loadThumbnail = async () => {
        setIsLoadingThumbnail(true);
        try {
          const downloadUrl = await attachmentUrlCache.getDownloadUrl(attachment.id);
          setThumbnailUrl(downloadUrl);
        } catch (error) {
          console.error('Failed to load image thumbnail:', error);
        } finally {
          setIsLoadingThumbnail(false);
        }
      };
      loadThumbnail();
    }
  }, [attachment, fileCategory, thumbnailUrl, isLoadingThumbnail]);

  // Handle preview
  const handlePreview = async () => {
    if (!canPreview || !showPreview || !attachment) return;

    setIsLoadingPreview(true);

    try {
      // Get download URL from cache or backend
      const downloadUrl = await attachmentUrlCache.getDownloadUrl(attachment.id);

      if (fileCategory === 'image') {
        setImagePreviewUrl(downloadUrl);
        setPreviewOpen(true);
      } else if (fileCategory === 'pdf') {
        setPdfUrl(downloadUrl);
        setPreviewOpen(true);
      } else if (fileCategory === 'text') {
        // For text files, fetch content and display
        try {
          const textResponse = await fetch(downloadUrl);
          const text = await textResponse.text();
          setTextContent(text);
          setPreviewOpen(true);
        } catch (error) {
          toast({
            title: 'Text Preview Error',
            description: 'Failed to load text content.',
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      toast({
        title: 'Preview Error',
        description: 'Failed to load file preview.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingPreview(false);
    }
  };

  // Handle download
  const handleDownload = async () => {
    if (!attachment) return;
    
    try {
      const downloadUrl = await attachmentUrlCache.getDownloadUrl(attachment.id);

      // Open download URL in new tab
      window.open(downloadUrl, '_blank');
    } catch (error) {
      toast({
        title: 'Download Error',
        description: 'Failed to generate download link.',
        variant: 'destructive',
      });
    }
  };

  // Cleanup URLs when component unmounts
  const handleClosePreview = () => {
    setImagePreviewUrl(null);
    setPdfUrl(null);
    setTextContent(null);
    setPreviewOpen(false);
  };

  if (isLoading) {
    return (
      <div className="relative group">
        <div className="w-16 h-16 border rounded-lg bg-card overflow-hidden relative">
          <div className="w-full h-full flex items-center justify-center bg-muted">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!attachment) {
    return null;
  }

  return (
    <>
      <div className="relative group">
        {fileCategory === 'image' ? (
          /* Larger Image Display */
          <div className="w-24 h-24 border rounded-lg bg-card overflow-hidden relative">
            {thumbnailUrl ? (
              <img
                src={thumbnailUrl}
                alt={attachment.filename}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-muted">
                {isLoadingThumbnail ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                ) : (
                  <Image className="h-6 w-6 text-muted-foreground" />
                )}
              </div>
            )}

            {/* Action Buttons Overlay */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1">
              {/* Preview Button */}
              {canPreview && showPreview && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePreview}
                  disabled={isLoadingPreview}
                  className="h-8 w-8 p-0 text-white hover:bg-white/20"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}

              {/* Download Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownload}
                className="h-8 w-8 p-0 text-white hover:bg-white/20"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          /* Icon Display for Non-Images */
          <div className="w-16 h-16 border rounded-lg bg-card overflow-hidden relative">
            <div className="w-full h-full flex items-center justify-center bg-muted">
              {getFileIcon(attachment.file_type)}
            </div>

            {/* Action Buttons Overlay */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1">
              {/* Preview Button */}
              {canPreview && showPreview && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePreview}
                  disabled={isLoadingPreview}
                  className="h-8 w-8 p-0 text-white hover:bg-white/20"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}

              {/* Download Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownload}
                className="h-8 w-8 p-0 text-white hover:bg-white/20"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={handleClosePreview}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{attachment.filename}</DialogTitle>
          </DialogHeader>
          
          <div className="overflow-auto">
            {/* Image Preview */}
            {imagePreviewUrl && (
              <div className="flex justify-center">
                <img
                  src={imagePreviewUrl}
                  alt={attachment.filename}
                  className="max-w-full max-h-[60vh] object-contain"
                />
              </div>
            )}

            {/* PDF Preview */}
            {pdfUrl && (
              <div className="h-[60vh]">
                <SimplePDFViewer
                  url={pdfUrl}
                  fileName={attachment.filename}
                  className="h-full"
                />
              </div>
            )}

            {/* Text Preview */}
            {textContent && (
              <div className="bg-muted p-4 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm font-mono overflow-auto max-h-[50vh]">
                  {textContent}
                </pre>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

export function SentMessageAttachments({ attachmentIds, fetchAttachmentMetadata, showPreview = true }: SentMessageAttachmentsProps) {
  if (!attachmentIds || attachmentIds.length === 0) return null;

  return (
    <div className="mb-2">
      <div className="flex items-center gap-1 mb-2">
        <span className="text-xs text-muted-foreground">
          {attachmentIds.length} file{attachmentIds.length > 1 ? 's' : ''}
        </span>
      </div>

      {/* Grid Layout for Square Previews */}
      <div className="flex flex-wrap gap-2">
        {attachmentIds.map((attachmentId) => (
          <SentAttachmentItem
            key={attachmentId}
            attachmentId={attachmentId}
            fetchAttachmentMetadata={fetchAttachmentMetadata}
            showPreview={showPreview}
          />
        ))}
      </div>
    </div>
  );
}
