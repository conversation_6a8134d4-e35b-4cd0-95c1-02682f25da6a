"use client";

import React, { useEffect, useRef, useState, useCallback, memo } from 'react';
import clientCookie from 'js-cookie';
import { Terminal, FolderIcon } from 'lucide-react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import { GripHorizontal } from 'lucide-react';
import { FileExplorer } from '../components/file-explorer';
import { ToolCall } from '../types';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Types for xterm
interface ITerminal {
  open: (container: HTMLElement) => void;
  write: (data: string) => void;
  writeln: (data: string) => void;
  onData: (callback: (data: string) => void) => void;
  onResize: (callback: (size: { rows: number; cols: number }) => void) => void;
  focus: () => void;
  dispose: () => void;
  loadAddon: (addon: unknown) => void;
  clear: () => void;
  scrollToTop: () => void;
  onKey: (callback: (e: { key: string; domEvent: KeyboardEvent }) => void) => void;
  cols: number;
  rows: number;
  buffer: {
    active: {
      baseY: number;
      viewportY: number;
      length: number;
    };
  };
  options: {
    scrollback?: number;
  };
}

interface IFitAddon {
  fit: () => void;
  proposeDimensions: () => { rows: number; cols: number } | undefined;
}

interface ConsoleCanvasProps {
  isVisible: boolean;
  className?: string;
  isUserActivated?: boolean;
  conversationId?: string;
  toolCalls?: ToolCall[];
}

const ConsoleCanvasComponent = function ConsoleCanvas({
  isVisible,
  className,
  isUserActivated = false,
  conversationId,
  toolCalls
}: ConsoleCanvasProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<ITerminal | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const fitAddonRef = useRef<IFitAddon | null>(null);
  const batchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messageBufferRef = useRef<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const isInitializedRef = useRef(false);
  const hasBeenUserActivatedRef = useRef(false);
  const currentConversationIdRef = useRef<string | undefined>(conversationId);
  const displayedToolsRef = useRef<Set<string>>(new Set());
  const waitingToolsRef = useRef<Set<string>>(new Set());
  const [currentPath, setCurrentPath] = useState('/workspace');
  const [activeView, setActiveView] = useState<'console' | 'explorer'>('console');

  // Function to clean up terminal and WebSocket
  const cleanup = useCallback(() => {
    if (batchTimeoutRef.current) {
      clearTimeout(batchTimeoutRef.current);
      batchTimeoutRef.current = null;
    }
    messageBufferRef.current = [];

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.close(1000, 'Cleanup');
    }
    if (xtermRef.current) {
      try {
        xtermRef.current.dispose();
      } catch (error) {
        console.warn('Error disposing terminal:', error);
      }
    }
    wsRef.current = null;
    xtermRef.current = null;
    setIsConnected(false);
    setIsLoading(true);
    isInitializedRef.current = false;
    hasBeenUserActivatedRef.current = false;
    currentConversationIdRef.current = undefined;
    displayedToolsRef.current.clear();
    waitingToolsRef.current.clear();
  }, []);

  // Handle conversation changes
  useEffect(() => {
    if (conversationId !== currentConversationIdRef.current) {
      currentConversationIdRef.current = conversationId;
      displayedToolsRef.current.clear();
      waitingToolsRef.current.clear();
      if (xtermRef.current && isConnected) {
        xtermRef.current.clear();
      }
    }
  }, [conversationId, isConnected]);

  // Track when user has activated the console
  useEffect(() => {
    if (isUserActivated) {
      hasBeenUserActivatedRef.current = true;
    }
  }, [isUserActivated]);

  // Auto-activate console when switching to console view if user has been activated
  useEffect(() => {
    if (activeView === 'console' && isUserActivated && !hasBeenUserActivatedRef.current) {
      hasBeenUserActivatedRef.current = true;
    }
  }, [activeView, isUserActivated]);

  // Display tool calls in the terminal
  useEffect(() => {
    if (!toolCalls || !xtermRef.current || !isConnected) return;

    // Handle waiting tool calls (not completed yet)
    const waitingToolCalls = toolCalls.filter(toolCall => {
      const notCompleted = toolCall.status !== 'completed';
      const notWaiting = !waitingToolsRef.current.has(toolCall.id);
      const isScriptTool = toolCall.name.includes('script') ||
                        toolCall.name.includes('run_') ||
                        toolCall.name.includes('execute') ||
                        toolCall.name.includes('bash') ||
                        toolCall.name.includes('command');

      return notCompleted && notWaiting && isScriptTool;
    });

    if (waitingToolCalls.length > 0) {
      waitingToolCalls.forEach((toolCall) => {
        try {
          const tool_args = toolCall.arguments;

          // Simple header for waiting script
          xtermRef.current!.write(`\x1b[33m[Script Executing...]\x1b[0m\r\n`);

          // Display the bash script simply
          if (tool_args?.script && typeof tool_args.script === 'string') {
            const scriptLines = tool_args.script.split('\n');
            scriptLines.forEach((line: string) => {
              if (line.trim()) {
                xtermRef.current!.write(line + '\r\n');
              }
            });
          }

          xtermRef.current!.write('\r\n');
          waitingToolsRef.current.add(toolCall.id);
        } catch (error) {
          console.error('Error displaying waiting tool call:', error);
        }
      });
    }

    // Filter for completed tool calls that haven't been displayed yet OR are currently waiting
    const completedToolCalls = toolCalls.filter(toolCall => {
      const hasOutput = !!toolCall.output;
      const isCompleted = toolCall.status === 'completed';
      const isScriptTool = toolCall.name.includes('script') ||
                        toolCall.name.includes('run_') ||
                        toolCall.name.includes('execute') ||
                        toolCall.name.includes('bash') ||
                        toolCall.name.includes('command');
      const notDisplayed = !displayedToolsRef.current.has(toolCall.id);
      const isWaiting = waitingToolsRef.current.has(toolCall.id);

      return hasOutput && isCompleted && isScriptTool && (notDisplayed || isWaiting);
    });

    if (completedToolCalls.length > 0) {
      completedToolCalls.forEach((toolCall) => {
        try {
          const tool_args = toolCall.arguments;
          const tool_output = toolCall.output;

          // If this was a waiting tool, clear that status first
          if (waitingToolsRef.current.has(toolCall.id)) {
            // Clear the terminal and redisplay all completed tools to maintain order
            xtermRef.current!.clear();
            waitingToolsRef.current.delete(toolCall.id);

            // Redisplay any other completed tools first
            const otherCompletedTools = toolCalls.filter(tc =>
              tc.output &&
              displayedToolsRef.current.has(tc.id) &&
              tc.status === 'completed' &&
              tc.name.includes('script') &&
              tc.id !== toolCall.id
            );

            otherCompletedTools.forEach(otherTool => {
              // Re-display other completed tools (simplified version)
              const otherArgs = otherTool.arguments;
              const otherOutput = otherTool.output;

              xtermRef.current!.write(`\x1b[32m[Script Completed]\x1b[0m\r\n`);

              if (otherArgs?.script && typeof otherArgs.script === 'string') {
                const scriptLines = otherArgs.script.split('\n');
                scriptLines.forEach((line: string) => {
                  if (line.trim()) {
                    xtermRef.current!.write(line + '\r\n');
                  }
                });
              }

              xtermRef.current!.write(`\x1b[36m[Output]\x1b[0m\r\n`);

              if (otherOutput?.output && typeof otherOutput.output === 'string') {
                const outputLines = otherOutput.output.split('\n');
                outputLines.forEach((line: string) => {
                  if (line.trim()) {
                    xtermRef.current!.write(line + '\r\n');
                  }
                });
              }

              xtermRef.current!.write('\r\n');
            });
          }

          // Simple header for completed script
          xtermRef.current!.write(`\x1b[32m[Script Completed]\x1b[0m\r\n`);

          // Display the bash script simply
          if (tool_args?.script && typeof tool_args.script === 'string') {
            const scriptLines = tool_args.script.split('\n');
            scriptLines.forEach((line: string) => {
              if (line.trim()) {
                xtermRef.current!.write(line + '\r\n');
              }
            });
          }

          xtermRef.current!.write(`\x1b[36m[Output]\x1b[0m\r\n`);

          // Display command output simply
          if (tool_output?.output && typeof tool_output.output === 'string') {
            const outputLines = tool_output.output.split('\n');
            outputLines.forEach((line: string) => {
              if (line.trim()) {
                xtermRef.current!.write(line + '\r\n');
              }
            });
          }

          xtermRef.current!.write('\r\n');

          displayedToolsRef.current.add(toolCall.id);
        } catch (error) {
          console.error('Error displaying tool call:', error);
        }
      });
    }
  }, [toolCalls, isConnected]);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible && xtermRef.current && fitAddonRef.current && isConnected && activeView === 'console') {
      // Fit terminal when it becomes visible
      setTimeout(() => {
        if (fitAddonRef.current) {
          try {
            fitAddonRef.current.fit();
          } catch (error) {
            console.warn('Error fitting terminal on visibility change:', error);
          }
        }
      }, 100);
    }
  }, [isVisible, isConnected, activeView]);

  // Initialize terminal
  useEffect(() => {
    if (!isVisible || !terminalRef.current || isInitializedRef.current || !hasBeenUserActivatedRef.current || activeView !== 'console') return;

    let xterm: ITerminal;
    let fitAddon: IFitAddon;
    let isCleanedUp = false;

    const initializeTerminal = async () => {
      try {
        const [
          { Terminal },
          { FitAddon },
          { WebLinksAddon },
        ] = await Promise.all([
          import('@xterm/xterm'),
          import('@xterm/addon-fit'),
          import('@xterm/addon-web-links'),
        ]);

        await import('@xterm/xterm/css/xterm.css');

        if (isCleanedUp) return;

        xterm = new Terminal({
          cursorBlink: true,
          fontSize: 12,
          fontFamily: 'JetBrains Mono, Menlo, Monaco, Consolas, monospace',
          theme: {
            background: '#1a1b1e',
            foreground: '#d4d4d4',
            cursor: '#ffffff',
            black: '#000000',
            red: '#e06c75',
            green: '#98c379',
            yellow: '#e5c07b',
            blue: '#61afef',
            magenta: '#c678dd',
            cyan: '#56b6c2',
            white: '#d4d4d4',
            brightBlack: '#5c6370',
            brightRed: '#e06c75',
            brightGreen: '#98c379',
            brightYellow: '#e5c07b',
            brightBlue: '#61afef',
            brightMagenta: '#c678dd',
            brightCyan: '#56b6c2',
            brightWhite: '#ffffff',
          },
          allowProposedApi: true,
          rows: 24,
          cols: 80,
          scrollback: 1000,
          smoothScrollDuration: 0,
          altClickMovesCursor: true,
          convertEol: false,
          wordSeparator: ' ()[]{}\'"',
          cursorStyle: 'block',
          macOptionIsMeta: true,
          macOptionClickForcesSelection: true,
          fastScrollModifier: 'alt',
          fastScrollSensitivity: 5,
          scrollSensitivity: 3,
          allowTransparency: false,
          disableStdin: false,
        }) as ITerminal;

        fitAddon = new FitAddon();
        const webLinksAddon = new WebLinksAddon();

        xterm.loadAddon(fitAddon);
        xterm.loadAddon(webLinksAddon);

        xtermRef.current = xterm;
        fitAddonRef.current = fitAddon;
        isInitializedRef.current = true;

        if (terminalRef.current && !isCleanedUp) {
          terminalRef.current.innerHTML = '';
          xterm.open(terminalRef.current);
          fitAddon.fit();
        }

        // Connect WebSocket
        const token = clientCookie.get('access_token');

        if (!token) {
          console.error('No access token found for WebSocket authentication');
          setIsLoading(false);
          setIsConnected(false);
          xterm.writeln('\r\n\x1b[31mAuthentication required - please login\x1b[0m');
          return;
        }

        // Note: Browser WebSocket API doesn't support custom headers during connection
        // We use query parameters for authentication as the backend supports both methods
        // Use secure WebSocket (wss) when page is loaded over HTTPS, otherwise use ws
        const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const wsUrl = new URL(`${process.env.NEXT_PUBLIC_API_URL?.replace(/^https?/, protocol)}/api/v1/console/ws`);
        wsUrl.searchParams.set('token', token);

        const ws = new WebSocket(wsUrl.toString());
        wsRef.current = ws;

        const removeListeners = () => {
          window.removeEventListener('resize', handleResize);
          document.removeEventListener('visibilitychange', handleVisibilityChange);
          if (resizeObserver) {
            resizeObserver.disconnect();
          }
        };

        ws.onopen = () => {
          if (isCleanedUp) {
            ws.close(1000, 'Cleanup during connection');
            return;
          }

          console.log('WebSocket connection established successfully');
          setIsConnected(true);
          xterm.clear();
          xterm.focus();

          const size = fitAddon.proposeDimensions();
          if (size) {
            try {
              ws.send(JSON.stringify({
                type: 'resize',
                cols: size.cols,
                rows: size.rows
              }));
            } catch (error) {
              console.warn('Error sending initial resize to WebSocket:', error);
            }
          }
        };

        const flushBuffer = () => {
          if (messageBufferRef.current.length > 0 && !isCleanedUp) {
            const combined = messageBufferRef.current.join('');
            xterm.write(combined);
            messageBufferRef.current = [];
          }
          batchTimeoutRef.current = null;
        };

        ws.onmessage = (event: MessageEvent) => {
          if (isCleanedUp) return;

          try {
            const data = JSON.parse(event.data);
            if (data.type === 'output') {
              const output = data.data;
              messageBufferRef.current.push(output);

              if (!batchTimeoutRef.current) {
                batchTimeoutRef.current = setTimeout(flushBuffer, 4);
              }
            } else if (data.type === 'prompt_refreshed') {
              xterm.focus();
            }
          } catch {
            const output = event.data;
            messageBufferRef.current.push(output);

            if (!batchTimeoutRef.current) {
              batchTimeoutRef.current = setTimeout(flushBuffer, 4);
            }
          }
        };

        ws.onclose = (event) => {
          if (!isCleanedUp) {
            if (batchTimeoutRef.current) {
              clearTimeout(batchTimeoutRef.current);
              flushBuffer();
            }
            setIsConnected(false);

            if (event.code === 1008) {
              console.error('WebSocket authentication failed:', event.reason);
              xterm.writeln('\r\n\x1b[31mAuthentication failed - please refresh and login again\x1b[0m');
            } else if (event.code === 1011) {
              console.error('WebSocket server error:', event.reason);
              xterm.writeln('\r\n\x1b[31mServer error - please try again later\x1b[0m');
            } else if (event.code !== 1000) {
              console.warn('WebSocket closed unexpectedly:', event.code, event.reason);
              xterm.writeln('\r\n\x1b[33mConnection lost - attempting to reconnect...\x1b[0m');
            } else {
              xterm.writeln('\r\n\x1b[31mDisconnected from terminal server\x1b[0m');
            }

            removeListeners();
          }
        };

        ws.onerror = (error) => {
          if (!isCleanedUp) {
            console.error('WebSocket connection error:', error);
            setIsConnected(false);
            xterm.writeln('\r\n\x1b[31mError connecting to terminal server - check your connection\x1b[0m');
            removeListeners();
          }
        };

        const dataHandler = (data: string) => {
          if (!isCleanedUp && ws.readyState === WebSocket.OPEN) {
            try {
              ws.send(JSON.stringify({
                type: 'input',
                data: data
              }));
            } catch (error) {
              console.warn('Error sending input to WebSocket:', error);
            }
          }
        };
        xterm.onData(dataHandler);

        const resizeHandler = (size: { rows: number; cols: number }) => {
          if (!isCleanedUp && ws.readyState === WebSocket.OPEN) {
            try {
              ws.send(JSON.stringify({
                type: 'resize',
                cols: size.cols,
                rows: size.rows
              }));
            } catch (error) {
              console.warn('Error sending resize to WebSocket:', error);
            }
          }
        };
        xterm.onResize(resizeHandler);

        const handleResize = () => {
          if (!isCleanedUp && fitAddon) {
            try {
              fitAddon.fit();
            } catch (error) {
              console.warn('Error fitting terminal:', error);
            }
          }
        };

        // Add multiple resize listeners for better responsiveness
        window.addEventListener('resize', handleResize);

        // Use ResizeObserver for container resize detection
        let resizeObserver: ResizeObserver | null = null;
        if (terminalRef.current) {
          resizeObserver = new ResizeObserver(handleResize);
          resizeObserver.observe(terminalRef.current);
        }

        // Initial fit with a small delay to ensure DOM is ready
        setTimeout(() => {
          if (!isCleanedUp) {
            handleResize();
          }
        }, 100);

        // Additional fit when terminal becomes visible
        const handleVisibilityChange = () => {
          if (!document.hidden && !isCleanedUp) {
            setTimeout(handleResize, 50);
          }
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);

        setIsLoading(false);

      } catch (error) {
        console.error('Failed to initialize terminal:', error);
        setIsLoading(false);
        cleanup();
      }
    };

    initializeTerminal();

    return () => {
      isCleanedUp = true;
      if (batchTimeoutRef.current) {
        clearTimeout(batchTimeoutRef.current);
      }
      cleanup();
    };
  }, [isVisible, cleanup, activeView]);

  if (!isVisible) return null;

  return (
    <div className={`h-full w-full bg-background ${className}`}>
      {/* View Toggle Header */}
      <div className="flex items-center justify-between border-b border-border p-2">
        <div className="flex items-center gap-2">
          <Button
            variant={activeView === 'console' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => setActiveView('console')}
            className="gap-2"
          >
            <Terminal className="h-4 w-4" />
            Console
          </Button>
          <Button
            variant={activeView === 'explorer' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => setActiveView('explorer')}
            className="gap-2"
          >
            <FolderIcon className="h-4 w-4" />
            Explorer
          </Button>
        </div>
      </div>

      {/* Content Area */}
      <div className="h-[calc(100%-45px)]">
        {activeView === 'explorer' ? (
          <FileExplorer
            isUserActivated={isUserActivated}
            currentPath={currentPath}
            onPathChange={setCurrentPath}
            conversationId={conversationId}
          />
        ) : (
          <div className="relative flex-1 h-full">
            {!hasBeenUserActivatedRef.current && (
              <div className="absolute inset-0 flex items-center justify-center bg-[#1a1b1e]/95 backdrop-blur-sm">
                <div className="flex flex-col items-center gap-3 text-center text-muted-foreground">
                  <Terminal className="h-8 w-8" />
                  <div className="flex flex-col gap-1">
                    <div className="text-sm font-medium">Console Terminal</div>
                    <div className="text-xs">Click on the Console tab to start the interactive terminal</div>
                  </div>
                </div>
              </div>
            )}
            {isLoading && hasBeenUserActivatedRef.current && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Initializing terminal...
                </div>
              </div>
            )}
            <div
              ref={terminalRef}
              className="absolute inset-0 overflow-hidden"
              style={{
                backgroundColor: '#1a1b1e',
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                width: '100%',
                minHeight: 0,
                minWidth: 0
              }}
            >
              <div
                style={{
                  flex: '1 1 auto',
                  minHeight: 0,
                  position: 'relative'
                }}
              >
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    padding: '8px'
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const ConsoleCanvas = memo(ConsoleCanvasComponent, (prevProps, nextProps) => {
  return (
    prevProps.isVisible === nextProps.isVisible &&
    prevProps.className === nextProps.className &&
    prevProps.isUserActivated === nextProps.isUserActivated &&
    prevProps.conversationId === nextProps.conversationId &&
    prevProps.toolCalls === nextProps.toolCalls
  );
});
