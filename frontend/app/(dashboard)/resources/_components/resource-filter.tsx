'use client';

import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  RESOURCE_REGION_OPTIONS,
  RESOURCE_STATUS_OPTIONS,
} from '@/features/resource/data/resource.constants';
import { ResourceQueryParams } from '@/features/resource/models/resource.type';
import { useFormFilter } from '@/hooks/use-form-filter';
import { RESOURCE_TYPE_OPTIONS } from '@/lib/constants/resource-types';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';

type FormFilter = WithPaginationDefaults<ResourceQueryParams>;

type Props = {
  defaultValues: FormFilter;
};

export function ResourceFilter({ defaultValues }: Props) {
  const { form, onSubmit } = useFormFilter<FormFilter>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'name',
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        <FormField
          control={control}
          name="name"
          render={({ field }) => (
            <FormItem className="grow md:max-w-lg">
              <FormControl>
                <Input
                  placeholder={`Search resource by name...`}
                  className={'w-full'}
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <FormField
            control={control}
            name="resource_type"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    options={RESOURCE_TYPE_OPTIONS}
                    mode="multiple"
                    {...field}
                    name="resource type"
                    PopoverContentProps={{
                      className: 'md:w-[340px]',
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    options={RESOURCE_STATUS_OPTIONS}
                    mode="multiple"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="region"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    mode="multiple"
                    options={RESOURCE_REGION_OPTIONS}
                    PopoverContentProps={{
                      className: 'md:w-[240px]',
                    }}
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* <Button variant="default" className="gap-2">
          <XIcon className="size-4" />
          Clear Filters
        </Button> */}
      </form>
    </Form>
  );
}
