// This file is auto-generated by @hey-api/openapi-ts

export type AccountEnvironement = 'production' | 'staging' | 'development';

export type ActivationResponse = {
    message: string;
    expires_at: string;
};

export type ActivationResult = {
    success: boolean;
    message: string;
    redirect_url?: (string | null);
    welcome_message?: (string | null);
};

export type Address = {
    city?: (string | null);
    country?: (string | null);
    line1?: (string | null);
    line2?: (string | null);
    postal_code?: (string | null);
    state?: (string | null);
};

export type AgentConnectorCreate = {
    agent_id: string;
    builtin_connector_ids: Array<(string)>;
    mcp_servers: Array<(string)>;
};

export type AgentConnectorResponse = {
    agent_id: string;
    builtin_connectors: Array<BuiltInConnectorResponse>;
    mcp_servers: Array<(string)>;
};

export type AgentConnectorUpdate = {
    builtin_connector_ids: Array<(string)>;
    mcp_servers: Array<(string)>;
};

export type AgentContextListInput = {
    agent_ids: Array<(string)>;
};

/**
 * Response model for paginated agent context list.
 *
 * Attributes:
 * data: List of agent context items
 * count: Total number of items available (before pagination)
 */
export type AgentContextListResponse = {
    data: Array<AgentContextRead>;
    count: number;
};

export type AgentContextRead = {
    title: string;
    context: string;
    is_active?: boolean;
    id: string;
    agent_id: string;
    created_at: string;
};

export type AgentContextUpdate = {
    title?: (string | null);
    context?: (string | null);
    is_active?: (boolean | null);
};

export type AgentCreate = {
    /**
     * The title/name of the agent
     */
    title: string;
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
};

export type AgentPublic = {
    /**
     * The title/name of the agent
     */
    title: string;
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
    id: string;
    is_active?: (boolean | null);
};

export type AgentsPublic = {
    data: Array<AgentPublic>;
    count: number;
};

/**
 * Defines the supported types of agents in the system.
 */
export type AgentType = 'conversation_agent' | 'autonomous_agent';

export type AgentTypeUsage = {
    agent_type: string;
    total_tokens: number;
};

export type AgentUpdate = {
    title?: (string | null);
    /**
     * Detailed description of the agent's purpose
     */
    description?: (string | null);
    /**
     * Type of the agent
     */
    type?: AgentType;
    /**
     * Custom instructions for the agent
     */
    instructions?: (string | null);
    workspace_id?: (string | null);
    is_active?: (boolean | null);
};

/**
 * Schema for creating a new alert
 */
export type AlertCreate = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
};

/**
 * Schema for list of alerts with pagination
 */
export type AlertList = {
    data: Array<AlertResponse>;
    total: number;
};

/**
 * Schema for alert response
 */
export type AlertResponse = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
    id: string;
    workspace_id: string;
    status: AlertStatus;
    created_at: string;
    updated_at?: (string | null);
};

export type AlertSeverity = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO';

export type AlertStatus = 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED';

/**
 * Summary of alerts by status for the last 30 days
 */
export type AlertStatusSummary = {
    /**
     * Count of alerts by status
     */
    status_counts: {
        [key: string]: (number);
    };
    /**
     * Total number of alerts in the period
     */
    total: number;
};

/**
 * Schema for updating an existing alert
 */
export type AlertUpdate = {
    title?: (string | null);
    description?: (string | null);
    severity?: (AlertSeverity | null);
    status?: (AlertStatus | null);
};

/**
 * Information about a generated presigned URL
 */
export type app__schemas__kb__PresignedUrlInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the file
     */
    storage_key: string;
    /**
     * Presigned URL for file upload
     */
    presigned_url: string;
};

/**
 * Response schema for task status operations
 */
export type app__schemas__kb__TaskStatusResponse = {
    /**
     * Celery task ID
     */
    task_id: string;
    /**
     * Task status (PENDING, PROGRESS, SUCCESS, FAILURE)
     */
    status: AsyncTaskStatus;
    /**
     * Progress percentage (0-100)
     */
    progress?: number;
    /**
     * Task result if completed
     */
    result?: ({
    [key: string]: unknown;
} | null);
    /**
     * Error message if failed
     */
    error?: (string | null);
    /**
     * Human-readable status message
     */
    status_message?: (string | null);
};

export type app__schemas__message_attachment__PresignedUrlInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Sanitized name of the file.
     */
    filename: string;
    /**
     * The key for the object in storage.
     */
    storage_key: string;
    /**
     * The presigned URL for uploading.
     */
    presigned_url: string;
};

export type app__schemas__message_attachment__TaskStatusResponse = {
    task_id: string;
    status: AsyncTaskStatus;
    status_message?: (string | null);
    progress?: number;
    result?: ({
    [key: string]: unknown;
} | null);
    error?: (string | null);
};

export type AsyncTaskStatus = 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';

export type AttachmentConfirmRequest = {
    /**
     * List of files that have been successfully uploaded.
     */
    uploaded_files: Array<UploadedAttachmentInfo>;
};

export type AttachmentDownloadResponse = {
    /**
     * The ID of the attachment.
     */
    attachment_id: string;
    /**
     * The presigned URL for downloading the file.
     */
    download_url: string;
};

export type AttachmentFileInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Original name of the file.
     */
    filename: string;
    /**
     * MIME type of the file.
     */
    content_type: string;
    /**
     * Size of the file in bytes.
     */
    file_size: number;
};

export type AttachmentMetadataResponse = {
    /**
     * The ID of the attachment.
     */
    id: string;
    /**
     * The name of the file.
     */
    filename: string;
    /**
     * The original name of the file.
     */
    original_filename: string;
    /**
     * The MIME type of the file.
     */
    file_type: string;
    /**
     * The size of the file in bytes.
     */
    file_size: number;
    /**
     * The storage key of the file.
     */
    storage_key: string;
    /**
     * The creation timestamp of the attachment.
     */
    created_at: string;
};

export type AttachmentPresignedUrlRequest = {
    /**
     * List of files to generate presigned URLs for.
     */
    files: Array<AttachmentFileInfo>;
};

export type AttachmentPresignedUrlResponse = {
    /**
     * List of presigned URLs and associated file info.
     */
    presigned_urls: Array<app__schemas__message_attachment__PresignedUrlInfo>;
};

export type AvailableUser = {
    id: string;
    email: string;
    full_name: string;
};

export type AvailableUsersCurrentWorkspace = {
    data: Array<AvailableUser>;
    count: number;
};

export type AWSAccountCreate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    account_id: string;
    access_key_id: string;
    secret_access_key: string;
    workspace_id: string;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type AWSAccountDetail = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
    access_key_id: string;
    secret_access_key: string;
    account_id: string;
};

export type AWSAccountPublic = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
};

export type AWSAccountsPublic = {
    data: Array<AWSAccountPublic>;
    count: number;
};

export type AWSAccountUpdate = {
    name?: (string | null);
    description?: (string | null);
    environment?: (string | null);
    account_id?: (string | null);
    regions?: (Array<(string)> | null);
    types?: (Array<(string)> | null);
    cron_pattern?: (string | null);
    access_key_id?: (string | null);
    secret_access_key?: (string | null);
};

export type BillingDetails = {
    address: Address;
    email?: (string | null);
    name?: (string | null);
    phone?: (string | null);
};

export type Body_login_login_access_token = {
    grant_type?: string;
    username: string;
    password: string;
    scope?: string;
    client_id?: (string | null);
    client_secret?: (string | null);
    slackOAuth?: boolean;
    appId?: (string | null);
    teamId?: (string | null);
};

export type Body_notifications_list_notifications = {
    type?: (Array<NotificationType> | null);
    status?: (Array<NotificationStatus> | null);
};

/**
 * Definition of built-in connectors available in the system
 */
export type BuiltInConnector = {
    id?: string;
    /**
     * Unique identifier for the connector
     */
    name: string;
    /**
     * Human-readable name for the connector
     */
    display_name: string;
    description?: (string | null);
    default_required_permission?: boolean;
    created_at?: string;
    updated_at?: string;
};

export type BuiltInConnectorResponse = {
    id: string;
    name?: (string | null);
    display_name?: (string | null);
    description?: (string | null);
};

export type CardDetails = {
    brand: string;
    country: string;
    display_brand: string;
    exp_month: number;
    exp_year: number;
    last4: string;
};

export type ChartDataPoint = {
    date: string;
    value: number;
};

/**
 * Enum for different types of charts available in the system
 */
export type ChartType = 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'radar' | 'step_area';

export type CheckoutSessionResponse = {
    checkout_session_url: string;
};

/**
 * Metadata for document citations
 */
export type CitationMetadata = {
    ref_id: number;
    doc_name: string;
    doc_section: string;
    text_snippet: string;
};

export type CloudProvider = 'AWS';

/**
 * Request to confirm file uploads and start ingestion
 */
export type ConfirmUploadsRequest = {
    /**
     * Information about successfully uploaded files
     */
    uploaded_files: Array<UploadedFileInfo>;
};

export type ConnectorCreate = {
    name: string;
    description?: (string | null);
    type: ConnectorType;
    approval?: boolean;
    config?: {
        [key: string]: unknown;
    };
};

export type ConnectorList = {
    data: Array<BuiltInConnector>;
    total: number;
};

export type ConnectorResponse = {
    id: string;
    name: string;
    description: (string | null);
    type: ConnectorType;
    approval?: boolean;
    config: {
        [key: string]: unknown;
    };
    workspace_id: string;
    created_by: string;
    updated_by: (string | null);
    created_at: string;
    updated_at: (string | null);
};

export type ConnectorType = 'bedrock_kb' | 'open_api';

export type ConnectorUpdate = {
    name?: (string | null);
    description?: (string | null);
    config?: ({
    [key: string]: unknown;
} | null);
    approval?: (boolean | null);
};

/**
 * Response model for a connector with its active status and permission settings in a workspace.
 *
 * Attributes:
 * id: Unique identifier for the workspace-connector association
 * name: Unique name of the connector
 * display_name: Human-readable name for the connector
 * description: Optional description of the connector
 * is_active: Whether the connector is active in this workspace
 * required_permission: Whether this tool requires human approval before execution
 */
export type ConnectorWithStatusResponse = {
    id: string;
    name: string;
    display_name: string;
    description?: (string | null);
    is_active: boolean;
    required_permission?: boolean;
};

export type ConversationCreateRequest = {
    agent_id: string;
    model_provider?: string;
    resource_id?: (string | null);
    instructions?: (string | null);
};

export type ConversationPublic = {
    id: string;
    agent_id: string;
    resource?: (Resource | null);
    name: string;
    model_provider: string;
    created_at: string;
};

/**
 * Response model for paginated conversations list.
 *
 * Attributes:
 * data: List of conversation items
 * count: Total number of items available (before pagination)
 */
export type ConversationsPublic = {
    data: Array<ConversationPublic>;
    count: number;
};

export type DailyMessageVolume = {
    date: string;
    message_count: number;
};

export type DailyTokenUsage = {
    date: string;
    total_tokens: number;
};

/**
 * Class for storing a piece of text and associated metadata.
 *
 * Example:
 *
 * .. code-block:: python
 *
 * from langchain_core.documents import Document
 *
 * document = Document(
 * page_content="Hello, world!",
 * metadata={"source": "https://example.com"}
 * )
 */
export type Document = {
    id?: (string | null);
    metadata?: {
        [key: string]: unknown;
    };
    page_content: string;
    type?: "Document";
};

export type DocumentKBRead = {
    name: string;
    type: DocumentType;
    url?: (string | null);
    deep_crawl?: boolean;
    file_name?: (string | null);
    file_type?: (string | null);
    object_name?: (string | null);
    embed_status?: AsyncTaskStatus;
    id: string;
    kb_id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    parent_id?: (string | null);
    children?: Array<DocumentKBRead>;
};

export type DocumentsKBRead = {
    data: Array<DocumentKBRead>;
    count: number;
};

export type DocumentType = 'url' | 'file';

/**
 * Response model for enterprise enquiry status messages
 */
export type EnterpriseEnquiryMessageResponse = {
    message: string;
};

export type EnterpriseEnquiryRequest = {
    first_name: string;
    last_name: string;
    work_title: string;
    work_email: string;
    company_name: string;
    estimated_monthly_cost: string;
    message: string;
    product_id: string;
};

export type ErrorResponse = {
    error: string;
    details?: (string | null);
};

/**
 * Enumeration for feedback types on agent responses.
 */
export type FeedbackType = 'good' | 'bad';

/**
 * Response for file content requests
 */
export type FileContentResponse = {
    /**
     * File content
     */
    content: string;
    /**
     * File path
     */
    path: string;
    /**
     * File name
     */
    name: string;
};

/**
 * Information about a file to generate a presigned URL for
 */
export type FileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * File MIME type
     */
    content_type: string;
    /**
     * File size in bytes
     */
    file_size: number;
};

/**
 * Response for directory listing
 */
export type FileListResponse = {
    /**
     * List of files and directories
     */
    files: Array<FileNode>;
    /**
     * Current directory path
     */
    current_path: string;
};

/**
 * Simplified file node matching frontend interface
 */
export type FileNode = {
    /**
     * Unique identifier for the file/directory
     */
    id: string;
    /**
     * File or directory name
     */
    name: string;
    /**
     * Full path
     */
    path: string;
    /**
     * File type (file or directory)
     */
    type: FileType;
    /**
     * Child nodes for directories
     */
    children?: (Array<FileNode> | null);
    /**
     * File content for files
     */
    content?: (string | null);
};

export type FileType = 'file' | 'directory';

export type HTTPValidationError = {
    detail?: Array<ValidationError>;
};

export type InvoiceResponse = {
    id: string;
    customer: string;
    status: string;
    amount_due: number;
    amount_paid: number;
    amount_remaining: number;
    currency: string;
    invoice_pdf?: (string | null);
    created: number;
    due_date?: (number | null);
    paid: boolean;
    payment_intent?: (string | null);
    subscription?: (string | null);
    total: number;
};

export type Item = {
    title: string;
    description?: (string | null);
    id?: string;
};

export type ItemCreate = {
    title: string;
    description?: (string | null);
};

export type ItemPublic = {
    title: string;
    description?: (string | null);
    id: string;
    owner_id: string;
};

export type ItemsPublic = {
    data: Array<ItemPublic>;
    count: number;
};

export type ItemUpdate = {
    title?: (string | null);
    description?: (string | null);
};

export type KBAccessLevel = 'private' | 'shared';

export type KBCreate = {
    title: string;
    description?: (string | null);
    access_level?: KBAccessLevel;
    usage_mode?: KBUsageMode;
    tags?: Array<(string)>;
    allowed_users?: Array<(string)>;
};

export type KBRead = {
    title: string;
    description?: (string | null);
    access_level: KBAccessLevel;
    usage_mode: KBUsageMode;
    tags?: Array<(string)>;
    id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    allowed_users?: Array<(string)>;
    owner_id: string;
};

export type KBsRead = {
    data: Array<KBRead>;
    count: number;
};

export type KBUpdate = {
    title?: (string | null);
    description?: (string | null);
    access_level?: (KBAccessLevel | null);
    tags?: (Array<(string)> | null);
    allowed_users?: (Array<(string)> | null);
    usage_mode?: (KBUsageMode | null);
};

export type KBUsageMode = 'manual' | 'agent_requested' | 'always';

/**
 * Schema for creating an MCP server
 */
export type MCPServerCreateSchema = {
    /**
     * Name of the MCP server
     */
    name: string;
    /**
     * Prefix for the MCP server
     */
    prefix: string;
    /**
     * Transport type
     */
    type?: MCPServerTransport;
    /**
     * Server configuration
     */
    config?: {
        [key: string]: unknown;
    };
    /**
     * Whether the server is active
     */
    is_active?: boolean;
    /**
     * Whether this is a builtin server
     */
    is_builtin?: boolean;
    /**
     * Tool permissions
     */
    tool_permissions?: Array<(string)>;
    /**
     * Enabled tools
     */
    tool_enabled?: Array<(string)>;
};

/**
 * Schema for paginated MCP server list response
 */
export type MCPServerListResponseSchema = {
    data: Array<MCPServerResponseSchema>;
    count: number;
};

/**
 * Schema for MCP server response
 */
export type MCPServerResponseSchema = {
    id: string;
    workspace_id: string;
    name: string;
    prefix: string;
    type: MCPServerTransport;
    config: {
        [key: string]: unknown;
    };
    is_active: boolean;
    is_builtin: boolean;
    tool_list: Array<(string)>;
    tool_permissions: Array<(string)>;
    tool_enabled: Array<(string)>;
    status: MCPServerStatus;
    status_message: string;
    status_updated_at: string;
    created_at: string;
    updated_at: string;
};

/**
 * MCP server status
 */
export type MCPServerStatus = 'connected' | 'error';

/**
 * MCP server transport
 */
export type MCPServerTransport = 'streamable_http' | 'sse';

/**
 * Schema for updating an MCP server
 */
export type MCPServerUpdateSchema = {
    /**
     * Name of the MCP server
     */
    name?: (string | null);
    /**
     * Prefix for the MCP server
     */
    prefix?: (string | null);
    /**
     * Transport type
     */
    type?: (MCPServerTransport | null);
    /**
     * Server configuration
     */
    config?: ({
    [key: string]: unknown;
} | null);
    /**
     * Whether the server is active
     */
    is_active?: (boolean | null);
    /**
     * Tool permissions
     */
    tool_permissions?: (Array<(string)> | null);
    /**
     * Enabled tools
     */
    tool_enabled?: (Array<(string)> | null);
};

export type Memory = {
    tags?: (Array<(string)> | null);
    task?: (string | null);
    solution?: (string | null);
    links?: (Array<(string)> | null);
    id: string;
    agent_role: string;
};

export type MemoryFilter = {
    agent_roles?: (Array<(string)> | null);
    limit?: number;
};

/**
 * Model for a memory node extracted from a conversation.
 */
export type MemoryNode = {
    tags?: (Array<(string)> | null);
    task?: (string | null);
    solution?: (string | null);
    links?: (Array<(string)> | null);
};

export type MemorysRead = {
    memories: Array<Memory>;
    count: number;
};

export type MemoryUpdate = {
    id: string;
    agent_role: string;
    memory: MemoryNode;
};

export type Message = {
    content: string;
    role?: string;
    is_interrupt?: boolean;
    interrupt_message?: (string | null);
    action_type?: MessageActionType;
    id?: string;
    conversation_id: string;
    created_at?: string;
    updated_at?: string;
    message_metadata?: {
        [key: string]: unknown;
    };
    is_deleted?: boolean;
};

/**
 * Enum for the type of action that can be taken by the message
 */
export type MessageActionType = 'none' | 'recommendation';

/**
 * Public schema for message display components
 */
export type MessageDisplayComponentPublic = {
    id: string;
    type: MessageDisplayComponentType;
    chart_type: (ChartType | null);
    title: (string | null);
    description: (string | null);
    data: {
        [key: string]: unknown;
    };
    config: {
        [key: string]: unknown;
    };
    position: number;
    created_at: string;
};

/**
 * Enum for display component types (currently supporting only tables and charts)
 */
export type MessageDisplayComponentType = 'table' | 'chart';

/**
 * Schema for creating message feedback
 */
export type MessageFeedbackCreate = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    message_id: string;
};

/**
 * Public schema for message feedback responses
 */
export type MessageFeedbackPublic = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    id: string;
    message_id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Schema for updating message feedback
 */
export type MessageFeedbackUpdate = {
    feedback_type?: (FeedbackType | null);
    reason?: (string | null);
    additional_comments?: (string | null);
};

export type MessageHistoryPublic = {
    limit: number;
    has_more: boolean;
    /**
     * list of messages with agent thoughts. Each message contains: id, message_id, position, thought, tool, tool_input, created_at, observation
     */
    data?: Array<{
        [key: string]: unknown;
    }>;
};

export type MessagePublic = {
    content: string;
    resume: boolean;
    approve: boolean;
    restore?: (boolean | null);
    message_id?: (string | null);
    action_type?: (MessageActionType | null);
    display_components?: (Array<MessageDisplayComponentPublic> | null);
    attachment_ids?: (Array<(string)> | null);
};

export type MessageStatistics = {
    total_messages: number;
    average_response_time: number;
    average_input_tokens_per_message: number;
    average_output_tokens_per_message: number;
    daily_message_volume: Array<DailyMessageVolume>;
    token_distribution_by_message_length: Array<TokenDistributionCategory>;
};

export type MetricCreate = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    resource_id: string;
};

export type MetricPublic = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    id: string;
    resource_id: string;
};

export type MetricRead = {
    name: string;
    value: number;
    unit: string;
    timestamp: string;
    type: MetricType;
    id: string;
    resource_id: string;
};

export type MetricsPublic = {
    data: Array<MetricPublic>;
    count: number;
};

export type MetricType = 'usage' | 'performance' | 'cost';

export type MetricUpdate = {
    name?: (string | null);
    value?: (number | null);
    unit?: (string | null);
    timestamp?: (string | null);
    type?: (MetricType | null);
};

export type ModuleSetting = {
    key: string;
    value?: {
        [key: string]: unknown;
    };
    created_at?: string;
    updated_at?: (string | null);
};

export type NewPassword = {
    token: string;
    new_password: string;
};

export type NodeType = 'start' | 'tool' | 'human_in_loop' | 'end' | 'output';

/**
 * Response model for paginated notifications list.
 *
 * Attributes:
 * data: List of notification items
 * count: Total number of items available (before pagination)
 */
export type NotificationList = {
    data: Array<NotificationResponse>;
    count: number;
};

export type NotificationResponse = {
    title: string;
    message: string;
    type?: NotificationType;
    status?: NotificationStatus;
    /**
     * Metadata for the notification
     */
    notification_metadata?: {
        [key: string]: unknown;
    };
    requires_action?: boolean;
    /**
     * URL for direct action
     */
    action_url?: (string | null);
    id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
    read_at?: (string | null);
    expires_at?: (string | null);
};

export type NotificationStatus = 'unread' | 'read' | 'archived';

export type NotificationType = 'info' | 'warning' | 'error' | 'interrupt';

export type PaymentMethodResponse = {
    id: string;
    billing_details: BillingDetails;
    card: CardDetails;
    created: number;
    customer: string;
    livemode: boolean;
    type: string;
};

export type PlanChangeRequestCreate = {
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    current_product_id: string;
    requested_price_id: string;
};

export type PlanChangeRequestResponse = {
    id: string;
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    status: string;
    customer_id: string;
    current_product_id: string;
    requested_product_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Request to generate presigned URLs for file uploads
 */
export type PresignedUrlRequest = {
    /**
     * ID of the knowledge base to upload files to
     */
    kb_id: string;
    /**
     * Information about files to generate presigned URLs for
     */
    files: Array<FileInfo>;
};

/**
 * Response with presigned URLs for file uploads
 */
export type PresignedUrlResponse = {
    /**
     * Knowledge base ID
     */
    kb_id: string;
    /**
     * Generated presigned URLs
     */
    presigned_urls: Array<app__schemas__kb__PresignedUrlInfo>;
};

export type PriceResponse = {
    id: string;
    stripe_price_id: string;
    product_id: string;
    active: boolean;
    amount: number;
    currency: string;
    interval: string;
};

export type ProductResponse = {
    id: string;
    name: string;
    description: string;
    stripe_product_id: string;
    active: boolean;
    prices?: (Array<PriceResponse> | null);
    quota_definition?: (QuotaDefinitionResponse | null);
    is_custom: boolean;
};

export type QuotaDefinitionResponse = {
    max_workspaces: number;
    max_members_per_workspace: number;
    max_fast_requests_per_month: (number | null);
};

export type QuotaInfo = {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
};

export type RecommendationCreate = {
    type: RecommendationType;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    resource_id: string;
};

export type RecommendationOveralPublic = {
    total_resource_scanned: number;
    total_well_optimized: number;
    total_optimization_opportunities: number;
    total_estimated_saving_amount: number;
};

export type RecommendationPublic = {
    type: RecommendationType;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    id: string;
    resource_id: string;
    resource: ResourcePublic;
};

export type RecommendationsPublic = {
    data: Array<RecommendationPublic>;
    count: number;
};

export type RecommendationStatus = 'pending' | 'implemented' | 'ignored' | 'in_progress';

export type RecommendationType = 'instance_rightsizing' | 'autoscaling_optimization' | 'auto_start_stop_optimization' | 'volume_optimization' | 'snapshot_cleanup' | 'reserved_instance_recommendation' | 'savings_plan_recommendation' | 'spot_instance_usage' | 'idle_resource_cleanup' | 'unused_eip_cleanup' | 'orphaned_snapshot_cleanup' | 'underutilized_ebs_cleanup' | 'serverless_migration' | 'container_adoption' | 'multi_az_optimization' | 'data_transfer_optimization' | 'cloudfront_optimization' | 'nat_gateway_optimization' | 'rds_optimization' | 'redshift_optimization' | 'dynamodb_optimization' | 's3_storage_class_optimization' | 'lambda_optimization' | 'tagging_improvement' | 'cost_allocation_improvement' | 'cost_anomaly_detection' | 'budget_alert_setup' | 'cost_explorer_usage' | 'modernize_legacy_services' | 'migrate_to_graviton' | 'compliance_optimization' | 'governance_improvement' | 'cross_region_optimization' | 'cross_account_optimization' | 'predictive_scaling' | 'ai_driven_optimization' | 'quantum_computing_readiness' | 'carbon_footprint_reduction' | 'renewable_energy_usage' | 'marketplace_alternative' | 'third_party_tool_recommendation' | 'custom_optimization' | 'other' | 'ec2_fleet_optimization' | 'spot_fleet_optimization' | 'graviton_migration' | 'predictive_scaling_optimization' | 'instance_connect_endpoint';

export type RecommendationUpdate = {
    type?: (RecommendationType | null);
    title?: (string | null);
    description?: (string | null);
    potential_savings?: (number | null);
    effort?: (string | null);
    risk?: (string | null);
    status?: (RecommendationStatus | null);
};

export type Report = {
    id?: string;
    conversation_id: string;
    workspace_id: string;
    title: string;
    description?: (string | null);
    sections?: {
        [key: string]: unknown;
    };
    executive_summary?: {
        [key: string]: unknown;
    };
    created_at?: string;
    updated_at?: string;
};

export type ResendActivationRequest = {
    email: string;
    captcha_token: string;
};

export type Resource = {
    name: string;
    arn: string;
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    id?: string;
    workspace_id: string;
    created_at?: string;
    updated_at?: string;
    is_active?: boolean;
};

export type ResourceCreate = {
    name: string;
    arn: string;
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    workspace_id: string;
};

export type ResourcePublic = {
    name: string;
    arn: string;
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    id: string;
    workspace: WorkspacePublic;
    total_recommendation: number;
    total_potential_saving: number;
    updated_at: string;
};

export type ResourceRead = {
    name: string;
    arn: string;
    tags: {
        [key: string]: (string);
    };
    configurations: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
    recommendations: Array<RecommendationPublic>;
    metrics?: ({
    [key: string]: Array<MetricRead>;
} | null);
};

export type ResourceSavingsReport = {
    rds_savings: Array<ChartDataPoint>;
    ec2_savings: Array<ChartDataPoint>;
    total_rds_savings: number;
    total_ec2_savings: number;
};

export type ResourcesPublic = {
    data: Array<ResourcePublic>;
    count: number;
};

export type ResourceStatus = 'stopped' | 'starting' | 'running' | 'found' | 'deleted';

export type ResourceType = 'EC2' | 'LAMBDA' | 'ECS' | 'EKS' | 'BATCH' | 'EC2_AUTO_SCALING' | 'ELASTIC_BEANSTALK' | 'APP_RUNNER' | 'RDS' | 'DYNAMODB' | 'ELASTICACHE' | 'NEPTUNE' | 'DOCUMENTDB' | 'OPENSEARCH' | 'REDSHIFT' | 'S3' | 'EBS' | 'EFS' | 'BACKUP' | 'VPC' | 'ELB' | 'CLOUDFORMATION' | 'CLOUDWATCH' | 'SQS' | 'SNS';

export type ResourceUpdate = {
    name?: (string | null);
    arn?: (string | null);
    tags?: {
        [key: string]: unknown;
    };
    configurations?: {
        [key: string]: unknown;
    };
    description?: (string | null);
    type: string;
    region: string;
    status?: ResourceStatus;
};

export type RetrieverConfig = {
    numberOfResults?: number;
    overrideSearchType?: ('HYBRID' | 'SEMANTIC' | null);
};

export type RunModeEnum = 'autonomous' | 'agent';

export type SavingSummaryReport = {
    potential_savings: number;
    potential_savings_percentage_change: number;
    save_opportunities: number;
    save_opportunities_percentage_change: number;
    total_saved: number;
    total_saved_percentage_change: number;
    active_saving: number;
    active_saving_percentage_change: number;
};

export type ScriptExecutionResponse = {
    status: string;
    result?: (string | null);
};

export type SearchResponse = {
    query: string;
    results: Array<Document>;
    total_found: number;
    execution_time: number;
};

export type ServiceSavingsData = {
    service: string;
    savings: number;
    percentage: number;
};

export type ServiceSavingsReport = {
    data: Array<ServiceSavingsData>;
    total_savings: number;
};

export type Setting = {
    id?: string;
    provider_name?: CloudProvider;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_patterns?: Array<(string)>;
};

export type ShareResponse = {
    share_id: string;
    is_shared: boolean;
    shared_at: string;
    shared_by: string;
};

export type StreamResponse = {
    type: string;
    content?: (string | null);
    message_id?: (string | null);
};

export type SubscriptionStatus = {
    id: string;
    customer_id: string;
    status: string;
    current_period_end: string;
    cancel_at?: (string | null);
    product_name: string;
    product_id?: (string | null);
    price_amount: number;
    price_currency: string;
    price_interval: string;
};

export type SummaryResponse = {
    query: string;
    summary: string;
    sources: Array<unknown>;
    citations: Array<CitationMetadata>;
    execution_time: number;
};

/**
 * Enumeration of possible task categories.
 */
export type TaskCategoryEnum = 'COST_OPTIMIZE' | 'OPERATIONAL' | 'SCALABILITY' | 'SECURITY' | 'OPERATIONAL_EFFICIENCY' | 'OTHER';

/**
 * Schema for task continue request.
 */
export type TaskContinueRequest = {
    /**
     * Message to continue the task with
     */
    message: string;
    /**
     * Whether to approve the task continuation
     */
    approve: boolean;
};

/**
 * Schema for task continue response.
 */
export type TaskContinueResponse = {
    /**
     * Task history ID that was continued
     */
    task_history_id: string;
    /**
     * Status of the continue operation
     */
    status: string;
    /**
     * Celery task ID for tracking
     */
    celery_task_id?: (string | null);
};

export type TaskCouldEnum = 'AWS' | 'AZURE' | 'GCP' | 'ALL';

/**
 * Schema for creating a new task.
 */
export type TaskCreate = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
};

/**
 * Schema for task delete response.
 */
export type TaskDeleteResponse = {
    status?: string;
};

/**
 * Enumeration of execution statuses for task.
 *
 * Attributes:
 * RUNNING: Currently executing
 * SUCCEEDED: Successfully completed
 * FAILED: Execution failed
 * CANCELLED: Execution cancelled
 * REQUIRED_APPROVAL: Execution requires approval
 */
export type TaskExecutionStatus = 'running' | 'succeeded' | 'failed' | 'cancelled' | 'required_approval';

/**
 * Execution history of a task conversation.
 */
export type TaskHistory = {
    id?: string;
    task_id: string;
    conversation_id: string;
    /**
     * Current task status
     */
    status: TaskExecutionStatus;
    /**
     * Message from the task execution: error or required action
     */
    message?: (string | null);
    /**
     * Celery task ID associated with the task
     */
    celery_task_id?: (string | null);
    /**
     * Timestamp when the task history was started
     */
    start_time?: string;
    /**
     * Timestamp when the task history was ended
     */
    end_time?: (string | null);
    /**
     * Time taken to run the task
     */
    run_time?: (number | null);
};

/**
 * Schema for paginated task list.
 */
export type TaskList = {
    data?: Array<TaskResponse>;
    total?: number;
};

/**
 * Enumeration of task priority levels.
 *
 * Attributes:
 * LOW (0): Regular priority, no urgency
 * MEDIUM (1): Moderate priority, should be done soon
 * HIGH (2): High priority, urgent attention needed
 * CRITICAL (3): Critical priority, requires immediate attention
 */
export type TaskPriority = 0 | 1 | 2 | 3;

/**
 * Schema for task response.
 */
export type TaskResponse = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    id: string;
    workspace_id: string;
    owner_id: string;
    scheduled_status?: (TaskScheduledStatus | null);
    execution_status?: (TaskExecutionStatus | null);
    error?: (string | null);
    last_run?: (string | null);
    next_run?: (string | null);
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
    enable?: boolean;
    task_history?: Array<TaskHistory>;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
};

/**
 * Enumeration of scheduled statuses for task.
 */
export type TaskScheduledStatus = 'pending' | 'scheduled';

/**
 * Enumeration of possible task services.
 */
export type TaskServiceEnum = 'ALL' | 'OTHER' | 'COMPUTE' | 'STORAGE' | 'SERVERLESS' | 'DATABASE' | 'NETWORK' | 'MESSAGING' | 'MANAGEMENT' | 'BILLING' | 'CROSS_SERVICE' | 'MONITORING' | 'STREAMING' | 'SECURITY';

/**
 * Schema for task stop response.
 */
export type TaskStopResponse = {
    task_id: string;
    conversation_id: string;
    status: string;
};

export type TaskTemplateCreate = {
    task: string;
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    service_name?: (string | null);
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule?: (string | null);
    context: string;
};

export type TaskTemplateList = {
    data: Array<TaskTemplateResponse>;
    total: number;
};

export type TaskTemplateResponse = {
    id: string;
    task: string;
    category: TaskCategoryEnum;
    service: TaskServiceEnum;
    service_name: string;
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule: (string | null);
    context: string;
    is_default: boolean;
    created_at: string;
    updated_at: (string | null);
};

export type TaskTemplateUpdate = {
    task?: (string | null);
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    run_mode?: (RunModeEnum | null);
    schedule?: (string | null);
    context?: (string | null);
};

/**
 * Schema for updating an existing task.
 */
export type TaskUpdate = {
    title?: (string | null);
    description?: (string | null);
    priority?: (TaskPriority | null);
    tags?: (Array<(string)> | null);
    schedule?: (string | null);
    agent_config?: {
        [key: string]: unknown;
    };
};

export type Token = {
    access_token: string;
    token_type?: string;
    workspace_id?: (string | null);
    is_first_login?: boolean;
    slack_oauth?: boolean;
    app_id?: (string | null);
    team_id?: (string | null);
};

export type TokenDistributionCategory = {
    category: string;
    percentage: number;
};

export type TokenUsageCreate = {
    /**
     * Unique identifier of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
};

/**
 * Schema for token usage response.
 *
 * Attributes:
 * id: Unique identifier for the usage record
 * message_id: ID of the associated message
 * input_tokens: Number of tokens in input text
 * output_tokens: Number of tokens in output text
 * model_id: ID of the AI model used
 * total_tokens: Total number of tokens used
 * created_at: Timestamp of record creation
 */
export type TokenUsageResponse = {
    /**
     * Unique identifier for the usage record
     */
    id: string;
    /**
     * ID of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
    /**
     * ID of the workspace
     */
    workspace_id: string;
    /**
     * Timestamp of record creation
     */
    created_at: string;
    /**
     * Timestamp of last update
     */
    updated_at?: (string | null);
    /**
     * Calculate total tokens from input and output tokens.
     */
    readonly total_tokens: number;
};

export type TopSavingsReport = {
    data: Array<RecommendationPublic>;
};

export type UpdatePassword = {
    current_password: string;
    new_password: string;
};

export type UploadCreate = {
    filename: string;
    file_size: number;
    file_type: string;
};

export type UploadedAttachmentInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Sanitized name of the file.
     */
    filename: string;
    /**
     * The key for the object in storage.
     */
    storage_key: string;
    /**
     * MIME type of the file.
     */
    content_type: string;
    /**
     * Size of the file in bytes.
     */
    file_size: number;
};

/**
 * Information about a successfully uploaded file
 */
export type UploadedFileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the uploaded file
     */
    storage_key: string;
    /**
     * File MIME type
     */
    content_type?: (string | null);
    /**
     * File size in bytes
     */
    file_size?: (number | null);
};

export type UploadPublic = {
    filename: string;
    file_size: number;
    file_type: string;
    id: string;
    status: UploadStatus;
    created_at: string;
};

export type UploadResponse = {
    id: string;
    upload_url: string;
    expires_in: number;
};

export type UploadStatus = 'pending' | 'in_progress' | 'completed' | 'failed';

export type URLsUploadRequest = {
    /**
     * URLs to crawl (required if source_type is website)
     */
    urls?: (Array<(string)> | null);
    /**
     * Whether to enable deep crawling for each URL
     */
    deep_crawls?: (Array<(boolean)> | null);
};

/**
 * Response schema for usage quota information.
 */
export type UsageQuotaResponse = {
    id: string;
    user_id: string;
    quota_used_messages: number;
    quota_used_tokens: number;
    reset_at: string;
    created_at: string;
    updated_at?: (string | null);
};

/**
 * Response schema for usage statistics.
 */
export type UsageStatistics = {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    quota_limit: number;
    quota_used: number;
    quota_remaining: number;
    usage_percentage: number;
    daily_token_usage: Array<DailyTokenUsage>;
    agent_type_stats: Array<AgentTypeUsage>;
};

export type UserCreate = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password: string;
};

export type UserDetail = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
    workspaces?: (Array<Workspace> | null);
    own_workspaces?: (Array<Workspace> | null);
    items?: (Array<Item> | null);
};

export type UserPublic = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
};

export type UserRegister = {
    email: string;
    password: string;
    full_name?: (string | null);
};

export type UsersPublic = {
    data: Array<UserPublic>;
    count: number;
};

export type UserUpdate = {
    email?: (string | null);
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password?: (string | null);
};

export type UserUpdateMe = {
    full_name?: (string | null);
    email?: (string | null);
    avatar_url?: (string | null);
};

export type ValidationError = {
    loc: Array<(string | number)>;
    msg: string;
    type: string;
};

export type WorkflowCreate = {
    name: string;
    description?: (string | null);
    workspace_id: string;
};

export type WorkflowNodeCreate = {
    type: NodeType;
    name: string;
    description?: (string | null);
    position: number;
    data?: {
        [key: string]: unknown;
    };
    workflow_id: string;
    parent_id?: (string | null);
};

export type WorkflowNodePublic = {
    type: NodeType;
    name: string;
    description?: (string | null);
    position: number;
    data?: {
        [key: string]: unknown;
    };
    id: string;
    status: WorkflowStatus;
};

export type WorkflowNodeUpdate = {
    name?: (string | null);
    description?: (string | null);
    position?: (number | null);
};

export type WorkflowPublic = {
    name: string;
    description?: (string | null);
    workspace_id: string;
    id: string;
    nodes: Array<WorkflowNodePublic>;
    status?: WorkflowStatus;
    created_at?: string;
    updated_at?: string;
};

export type WorkflowsPublic = {
    data: Array<WorkflowPublic>;
    count: number;
};

export type WorkflowStatus = 'created' | 'unvalidated' | 'running' | 'pending' | 'completed' | 'error';

export type WorkflowUpdate = {
    name?: (string | null);
    description?: (string | null);
    workspace_id?: (string | null);
};

export type Workspace = {
    name: string;
    description?: (string | null);
    id?: string;
    owner_id: string;
    created_at?: string;
    updated_at?: string;
    is_default?: boolean;
    is_deleted?: boolean;
};

export type WorkspaceCreate = {
    name: string;
    description?: (string | null);
    owner_id?: (string | null);
};

export type WorkspaceDetail = {
    name: string;
    description?: (string | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
    aws_account?: (AWSAccountDetail | null);
    settings: (WorkspaceSetting | null);
    provider_settings: Setting;
};

export type WorkspacePublic = {
    name: string;
    description?: (string | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
};

/**
 * Settings for a workspace
 */
export type WorkspaceSetting = {
    id?: string;
    workspace_id: string;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type WorkspacesPublic = {
    data: Array<WorkspacePublic>;
    count: number;
};

export type WorkspaceUpdate = {
    name: string;
    description?: (string | null);
};

export type AgentConnectorsCreateAgentConnectorData = {
    requestBody: AgentConnectorCreate;
};

export type AgentConnectorsCreateAgentConnectorResponse = (AgentConnectorResponse);

export type AgentConnectorsGetAgentConnectorData = {
    agentId: string;
};

export type AgentConnectorsGetAgentConnectorResponse = (AgentConnectorResponse);

export type AgentConnectorsUpdateAgentConnectorData = {
    agentId: string;
    requestBody: AgentConnectorUpdate;
};

export type AgentConnectorsUpdateAgentConnectorResponse = (AgentConnectorResponse);

export type AgentConnectorsDeleteAgentConnectorData = {
    agentId: string;
};

export type AgentConnectorsDeleteAgentConnectorResponse = (void);

export type AgentConnectorsGetAgentConnectorsByWorkspaceIdResponse = (Array<AgentConnectorResponse>);

export type AgentContextUpdateAgentContextData = {
    agentId: string;
    requestBody: AgentContextUpdate;
};

export type AgentContextUpdateAgentContextResponse = (AgentContextRead);

export type AgentContextGetAgentContextData = {
    agentId: string;
};

export type AgentContextGetAgentContextResponse = ((AgentContextRead | null));

export type AgentContextGetAgentContextsData = {
    requestBody: AgentContextListInput;
};

export type AgentContextGetAgentContextsResponse = (AgentContextListResponse);

export type AgentsReadAgentsData = {
    limit?: number;
    skip?: number;
};

export type AgentsReadAgentsResponse = (AgentsPublic);

export type AgentsCreateAgentData = {
    requestBody: AgentCreate;
};

export type AgentsCreateAgentResponse = (AgentPublic);

export type AgentsReadAgentData = {
    id: string;
};

export type AgentsReadAgentResponse = (AgentPublic);

export type AgentsUpdateAgentData = {
    id: string;
    requestBody: AgentUpdate;
};

export type AgentsUpdateAgentResponse = (AgentPublic);

export type AgentsDeleteAgentData = {
    id: string;
};

export type AgentsDeleteAgentResponse = (Message);

export type AgentsInitDefaultAgentsData = {
    workspaceId: string;
};

export type AgentsInitDefaultAgentsResponse = (Message);

export type AlertsGetAlertStatusSummaryResponse = (AlertStatusSummary);

export type AlertsCreateAlertData = {
    requestBody: AlertCreate;
};

export type AlertsCreateAlertResponse = (AlertResponse);

export type AlertsListAlertsData = {
    limit?: number;
    severity?: (AlertSeverity | null);
    skip?: number;
    /**
     * Field to sort by
     */
    sortBy?: string;
    /**
     * Sort in descending order
     */
    sortDesc?: boolean;
    status?: (AlertStatus | null);
};

export type AlertsListAlertsResponse = (AlertList);

export type AlertsGetAlertData = {
    alertId: string;
};

export type AlertsGetAlertResponse = (AlertResponse);

export type AlertsUpdateAlertData = {
    alertId: string;
    requestBody: AlertUpdate;
};

export type AlertsUpdateAlertResponse = (AlertResponse);

export type AlertsDeleteAlertData = {
    alertId: string;
};

export type AlertsDeleteAlertResponse = (unknown);

export type AlertsUpdateAlertStatusData = {
    alertId: string;
    status: AlertStatus;
};

export type AlertsUpdateAlertStatusResponse = (AlertResponse);

export type AlertsMarkAllAlertsAcknowledgedResponse = (unknown);

export type AttachmentsGenerateAttachmentPresignedUrlsData = {
    requestBody: AttachmentPresignedUrlRequest;
};

export type AttachmentsGenerateAttachmentPresignedUrlsResponse = (AttachmentPresignedUrlResponse);

export type AttachmentsConfirmAttachmentUploadsData = {
    requestBody: AttachmentConfirmRequest;
};

export type AttachmentsConfirmAttachmentUploadsResponse = (app__schemas__message_attachment__TaskStatusResponse);

export type AttachmentsGetAttachmentTaskStatusData = {
    taskId: string;
};

export type AttachmentsGetAttachmentTaskStatusResponse = (app__schemas__message_attachment__TaskStatusResponse);

export type AttachmentsGetAttachmentDownloadUrlData = {
    attachmentId: string;
};

export type AttachmentsGetAttachmentDownloadUrlResponse = (AttachmentDownloadResponse);

export type AttachmentsGetAttachmentMetadataData = {
    attachmentId: string;
};

export type AttachmentsGetAttachmentMetadataResponse = (AttachmentMetadataResponse);

export type AuthRegisterData = {
    requestBody: UserRegister;
};

export type AuthRegisterResponse = (ActivationResponse);

export type AuthActivateAccountData = {
    token: string;
};

export type AuthActivateAccountResponse = (ActivationResult);

export type AuthResendActivationData = {
    requestBody: ResendActivationRequest;
};

export type AuthResendActivationResponse = (ActivationResponse);

export type AutonomousAgentsCreateConversationData = {
    requestBody: ConversationCreateRequest;
};

export type AutonomousAgentsCreateConversationResponse = (ConversationPublic);

export type AutonomousAgentsGetConversationsData = {
    agentId?: (string | null);
    /**
     * Maximum number of records to return
     */
    limit?: number;
    modelProvider?: (string | null);
    resourceId?: (string | null);
    /**
     * Number of records to skip for pagination
     */
    skip?: number;
};

export type AutonomousAgentsGetConversationsResponse = (ConversationsPublic);

export type AutonomousAgentsGetMessagesHistoryData = {
    conversationId: string;
    limit?: number;
};

export type AutonomousAgentsGetMessagesHistoryResponse = (MessageHistoryPublic);

export type AutonomousAgentsChatStreamData = {
    conversationId: string;
    requestBody: MessagePublic;
};

export type AutonomousAgentsChatStreamResponse = (StreamResponse);

export type AutonomousAgentsRenameConversationData = {
    conversationId: string;
    name: string;
};

export type AutonomousAgentsRenameConversationResponse = (unknown);

export type AutonomousAgentsDeleteConversationData = {
    conversationId: string;
};

export type AutonomousAgentsDeleteConversationResponse = (unknown);

export type AwsAccountsReadAwsAccountsData = {
    limit?: number;
    skip?: number;
};

export type AwsAccountsReadAwsAccountsResponse = (AWSAccountsPublic);

export type AwsAccountsCreateAwsAccountData = {
    requestBody: AWSAccountCreate;
};

export type AwsAccountsCreateAwsAccountResponse = (AWSAccountPublic);

export type AwsAccountsReadAwsAccountData = {
    id: string;
};

export type AwsAccountsReadAwsAccountResponse = (AWSAccountPublic);

export type AwsAccountsUpdateAwsAccountData = {
    id: string;
    requestBody: AWSAccountUpdate;
};

export type AwsAccountsUpdateAwsAccountResponse = (AWSAccountPublic);

export type AwsAccountsDeleteAwsAccountData = {
    id: string;
};

export type AwsAccountsDeleteAwsAccountResponse = (Message);

export type BuiltinConnectorsListWorkspaceConnectorsData = {
    activeOnly?: boolean;
    limit?: number;
    skip?: number;
};

export type BuiltinConnectorsListWorkspaceConnectorsResponse = (Array<ConnectorWithStatusResponse>);

export type BuiltinConnectorsUpdateConnectorForWorkspaceData = {
    connectorId: string;
    isActive: boolean;
};

export type BuiltinConnectorsUpdateConnectorForWorkspaceResponse = (boolean);

export type BuiltinConnectorsUpdateConnectorPermissionData = {
    connectorId: string;
    requiredPermission: boolean;
};

export type BuiltinConnectorsUpdateConnectorPermissionResponse = (boolean);

export type ConnectorsCreateConnectorData = {
    requestBody: ConnectorCreate;
};

export type ConnectorsCreateConnectorResponse = (ConnectorResponse);

export type ConnectorsListConnectorsData = {
    limit?: number;
    skip?: number;
};

export type ConnectorsListConnectorsResponse = (ConnectorList);

export type ConnectorsGetConnectorData = {
    id: string;
};

export type ConnectorsGetConnectorResponse = (ConnectorResponse);

export type ConnectorsUpdateConnectorData = {
    id: string;
    requestBody: ConnectorUpdate;
};

export type ConnectorsUpdateConnectorResponse = (ConnectorResponse);

export type ConnectorsDeleteConnectorData = {
    id: string;
};

export type ConnectorsDeleteConnectorResponse = (Message);

export type ConsoleProxyGetFilesResponse = (FileListResponse);

export type ConsoleProxyGetFileContentData = {
    /**
     * File path
     */
    path: string;
};

export type ConsoleProxyGetFileContentResponse = (FileContentResponse);

export type FilesCreateUploadUrlData = {
    requestBody: UploadCreate;
};

export type FilesCreateUploadUrlResponse = (UploadResponse);

export type FilesCheckUploadStatusData = {
    id: string;
};

export type FilesCheckUploadStatusResponse = (UploadPublic);

export type GoogleGoogleLoginResponse = (unknown);

export type GoogleGoogleCallbackResponse = (Token);

export type ItemsReadItemsData = {
    limit?: number;
    skip?: number;
};

export type ItemsReadItemsResponse = (ItemsPublic);

export type ItemsCreateItemData = {
    requestBody: ItemCreate;
};

export type ItemsCreateItemResponse = (ItemPublic);

export type ItemsReadItemData = {
    id: string;
};

export type ItemsReadItemResponse = (ItemPublic);

export type ItemsUpdateItemData = {
    id: string;
    requestBody: ItemUpdate;
};

export type ItemsUpdateItemResponse = (ItemPublic);

export type ItemsDeleteItemData = {
    id: string;
};

export type ItemsDeleteItemResponse = (Message);

export type KnowledgeBaseCreateKbData = {
    requestBody: KBCreate;
};

export type KnowledgeBaseCreateKbResponse = (KBRead);

export type KnowledgeBaseGetKbsData = {
    accessLevel?: (string | null);
    limit?: number;
    search?: (string | null);
    skip?: number;
    usageMode?: (string | null);
};

export type KnowledgeBaseGetKbsResponse = (KBsRead);

export type KnowledgeBaseGetAvailableUsersResponse = (AvailableUsersCurrentWorkspace);

export type KnowledgeBaseGetPointUsageResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGetKbByIdData = {
    kbId: string;
};

export type KnowledgeBaseGetKbByIdResponse = (KBRead);

export type KnowledgeBaseUpdateKbData = {
    kbId: string;
    requestBody: KBUpdate;
};

export type KnowledgeBaseUpdateKbResponse = (KBRead);

export type KnowledgeBaseDeleteKbData = {
    kbId: string;
};

export type KnowledgeBaseDeleteKbResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGeneratePresignedUrlsData = {
    kbId: string;
    requestBody: PresignedUrlRequest;
};

export type KnowledgeBaseGeneratePresignedUrlsResponse = (PresignedUrlResponse);

export type KnowledgeBaseConfirmFileUploadsData = {
    kbId: string;
    requestBody: ConfirmUploadsRequest;
};

export type KnowledgeBaseConfirmFileUploadsResponse = (app__schemas__kb__TaskStatusResponse);

export type KnowledgeBaseUploadUrlsData = {
    kbId: string;
    requestBody: URLsUploadRequest;
};

export type KnowledgeBaseUploadUrlsResponse = (app__schemas__kb__TaskStatusResponse);

export type KnowledgeBaseListDocumentsData = {
    kbId: string;
    limit?: number;
    search?: (string | null);
    skip?: number;
};

export type KnowledgeBaseListDocumentsResponse = (DocumentsKBRead);

export type KnowledgeBaseGetDocumentContentData = {
    kbId: string;
    objectName: string;
};

export type KnowledgeBaseGetDocumentContentResponse = (string);

export type KnowledgeBaseDeleteDocumentData = {
    documentId: string;
    kbId: string;
    objectName: string;
};

export type KnowledgeBaseDeleteDocumentResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGetTaskStatusData = {
    taskId: string;
};

export type KnowledgeBaseGetTaskStatusResponse = (app__schemas__kb__TaskStatusResponse);

export type KnowledgeBaseRuntimeSearchData = {
    kbId: string;
    query: string;
    requestBody: RetrieverConfig;
};

export type KnowledgeBaseRuntimeSearchResponse = (SearchResponse);

export type KnowledgeBaseRuntimeSummarizeData = {
    kbId: string;
    query: string;
    requestBody: RetrieverConfig;
};

export type KnowledgeBaseRuntimeSummarizeResponse = (SummaryResponse);

export type LoginLoginAccessTokenData = {
    formData: Body_login_login_access_token;
};

export type LoginLoginAccessTokenResponse = (Token);

export type LoginTestTokenResponse = (UserPublic);

export type LoginRecoverPasswordData = {
    email: string;
};

export type LoginRecoverPasswordResponse = (Message);

export type LoginResetPasswordData = {
    requestBody: NewPassword;
};

export type LoginResetPasswordResponse = (Message);

export type LoginRecoverPasswordHtmlContentData = {
    email: string;
};

export type LoginRecoverPasswordHtmlContentResponse = (string);

export type McpServerGetMcpServersResponse = (MCPServerListResponseSchema);

export type McpServerCreateMcpServerData = {
    requestBody: MCPServerCreateSchema;
};

export type McpServerCreateMcpServerResponse = (MCPServerResponseSchema);

export type McpServerGetMcpServerData = {
    serverId: string;
};

export type McpServerGetMcpServerResponse = (MCPServerResponseSchema);

export type McpServerUpdateMcpServerData = {
    requestBody: MCPServerUpdateSchema;
    serverId: string;
};

export type McpServerUpdateMcpServerResponse = (MCPServerResponseSchema);

export type McpServerDeleteMcpServerData = {
    serverId: string;
};

export type McpServerDeleteMcpServerResponse = (Message);

export type McpServerRefreshMcpServerData = {
    serverId: string;
};

export type McpServerRefreshMcpServerResponse = (MCPServerResponseSchema);

export type MemoryGetMemoryData = {
    requestBody: MemoryFilter;
};

export type MemoryGetMemoryResponse = (MemorysRead);

export type MemoryDeleteMemoryData = {
    agentRole: string;
    id: string;
};

export type MemoryDeleteMemoryResponse = (unknown);

export type MemoryUpdateMemoryData = {
    requestBody: MemoryUpdate;
};

export type MemoryUpdateMemoryResponse = (unknown);

export type MessageFeedbackGetMessageFeedbackData = {
    messageId: string;
};

export type MessageFeedbackGetMessageFeedbackResponse = ((MessageFeedbackPublic | null));

export type MessageFeedbackUpdateMessageFeedbackData = {
    messageId: string;
    requestBody: MessageFeedbackUpdate;
};

export type MessageFeedbackUpdateMessageFeedbackResponse = (MessageFeedbackPublic);

export type MessageFeedbackDeleteMessageFeedbackData = {
    messageId: string;
};

export type MessageFeedbackDeleteMessageFeedbackResponse = (unknown);

export type MessageFeedbackCreateMessageFeedbackData = {
    requestBody: MessageFeedbackCreate;
};

export type MessageFeedbackCreateMessageFeedbackResponse = (MessageFeedbackPublic);

export type MetricsReadMetricsData = {
    endDate?: (string | null);
    limit?: number;
    resourceId?: (string | null);
    skip?: number;
    startDate?: (string | null);
};

export type MetricsReadMetricsResponse = (MetricsPublic);

export type MetricsCreateMetricData = {
    requestBody: MetricCreate;
};

export type MetricsCreateMetricResponse = (MetricPublic);

export type MetricsReadMetricData = {
    id: string;
};

export type MetricsReadMetricResponse = (MetricPublic);

export type MetricsUpdateMetricData = {
    id: string;
    requestBody: MetricUpdate;
};

export type MetricsUpdateMetricResponse = (MetricPublic);

export type MetricsDeleteMetricData = {
    id: string;
};

export type MetricsDeleteMetricResponse = (Message);

export type ModuleSettingGetModuleSettingsResponse = (Array<ModuleSetting>);

export type NotificationsListNotificationsData = {
    limit?: number;
    requestBody?: Body_notifications_list_notifications;
    requiresAction?: (boolean | null);
    skip?: number;
    timeframe?: (string | null);
};

export type NotificationsListNotificationsResponse = (NotificationList);

export type NotificationsMarkNotificationReadData = {
    notificationId: string;
};

export type NotificationsMarkNotificationReadResponse = (NotificationResponse);

export type NotificationsMarkAllNotificationsReadData = {
    requestBody?: (Array<NotificationType> | null);
};

export type NotificationsMarkAllNotificationsReadResponse = (unknown);

export type QuotasCreateUsageData = {
    requestBody: TokenUsageCreate;
};

export type QuotasCreateUsageResponse = (TokenUsageResponse);

export type QuotasGetMessagesStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type QuotasGetMessagesStatisticsResponse = (MessageStatistics);

export type QuotasCreateUsageQuotaData = {
    userId: string;
};

export type QuotasCreateUsageQuotaResponse = (UsageQuotaResponse);

export type QuotasGetUsageQuotaData = {
    userId: string;
};

export type QuotasGetUsageQuotaResponse = (UsageQuotaResponse);

export type QuotasResetUserQuotaData = {
    userId: string;
};

export type QuotasResetUserQuotaResponse = (UsageQuotaResponse);

export type QuotasGetUsageStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
    userId: string;
};

export type QuotasGetUsageStatisticsResponse = (UsageStatistics);

export type QuotasGetQuotaInfoData = {
    userId: string;
};

export type QuotasGetQuotaInfoResponse = (QuotaInfo);

export type RecommendationsGetRecomendationOveralResponse = (RecommendationOveralPublic);

export type RecommendationsReadRecommendationsData = {
    endDate?: (string | null);
    limit?: number;
    orderBy?: (string | null);
    orderDirection?: string;
    recommendationType?: Array<(string)>;
    resourceId?: Array<(string)>;
    resourceType?: Array<(string)>;
    search?: (string | null);
    skip?: number;
    startDate?: (string | null);
    status?: Array<(string)>;
};

export type RecommendationsReadRecommendationsResponse = (RecommendationsPublic);

export type RecommendationsCreateRecommendationData = {
    requestBody: RecommendationCreate;
};

export type RecommendationsCreateRecommendationResponse = (RecommendationPublic);

export type RecommendationsReadRecommendationData = {
    id: string;
};

export type RecommendationsReadRecommendationResponse = (RecommendationPublic);

export type RecommendationsUpdateRecommendationData = {
    id: string;
    requestBody: RecommendationUpdate;
};

export type RecommendationsUpdateRecommendationResponse = (RecommendationPublic);

export type RecommendationsDeleteRecommendationData = {
    id: string;
};

export type RecommendationsDeleteRecommendationResponse = (Message);

export type RecommendationsUpdateRecommendationStatusData = {
    id: string;
    status: RecommendationStatus;
};

export type RecommendationsUpdateRecommendationStatusResponse = (RecommendationPublic);

export type ReportsGetSavingsSummaryData = {
    endDate?: (string | null);
    previousEndDate?: (string | null);
    previousStartDate?: (string | null);
    startDate?: (string | null);
};

export type ReportsGetSavingsSummaryResponse = (SavingSummaryReport);

export type ReportsGetSavingsByResourceData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type ReportsGetSavingsByResourceResponse = (ResourceSavingsReport);

export type ReportsGetTopPotentialSavingsData = {
    /**
     * End date in ISO format
     */
    endDate?: (string | null);
    limit?: number;
    /**
     * Start date in ISO format
     */
    startDate?: (string | null);
};

export type ReportsGetTopPotentialSavingsResponse = (TopSavingsReport);

export type ReportsGetSavingsByServiceData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type ReportsGetSavingsByServiceResponse = (ServiceSavingsReport);

export type ReportsGetReportByConversationData = {
    conversationId: string;
};

export type ReportsGetReportByConversationResponse = (Report);

export type ResourcesReadResourcesData = {
    limit?: number;
    name?: string;
    region?: Array<(string)>;
    resourceType?: Array<(string)>;
    skip?: number;
    status?: Array<(string)>;
};

export type ResourcesReadResourcesResponse = (ResourcesPublic);

export type ResourcesCreateResourceData = {
    requestBody: ResourceCreate;
};

export type ResourcesCreateResourceResponse = (ResourcePublic);

export type ResourcesReadResourceData = {
    id: string;
};

export type ResourcesReadResourceResponse = (ResourceRead);

export type ResourcesUpdateResourceData = {
    id: string;
    requestBody: ResourceUpdate;
};

export type ResourcesUpdateResourceResponse = (ResourcePublic);

export type ResourcesDeleteResourceData = {
    id: string;
};

export type ResourcesDeleteResourceResponse = (Message);

export type SampleDataCreateSampleResourcesData = {
    daysBack?: number;
    metricsPerResource?: number;
    resourceCount?: number;
    resourceType: ResourceType;
};

export type SampleDataCreateSampleResourcesResponse = (unknown);

export type SampleDataCreateSampleMetricsData = {
    daysBack?: number;
    numPoints?: number;
    resourceType: ResourceType;
};

export type SampleDataCreateSampleMetricsResponse = (unknown);

export type SampleDataCreateSampleRecommendationsData = {
    totalRecord?: number;
};

export type SampleDataCreateSampleRecommendationsResponse = (RecommendationsPublic);

export type ShareChatCreateShareLinkData = {
    conversationId: string;
};

export type ShareChatCreateShareLinkResponse = (ShareResponse);

export type ShareChatRevokeShareLinkData = {
    conversationId: string;
};

export type ShareChatRevokeShareLinkResponse = (unknown);

export type ShareChatGetShareLinkData = {
    conversationId: string;
};

export type ShareChatGetShareLinkResponse = (ShareResponse);

export type ShareChatGetSharedConversationData = {
    limit?: number;
    shareId: string;
};

export type ShareChatGetSharedConversationResponse = (MessageHistoryPublic);

export type SubscriptionsGetAvailablePlansResponse = (Array<ProductResponse>);

export type SubscriptionsGetUserSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type SubscriptionsGetWorkspaceSubscriptionStatusData = {
    workspaceId: string;
};

export type SubscriptionsGetWorkspaceSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type SubscriptionsCreateCheckoutSessionData = {
    priceId: string;
};

export type SubscriptionsCreateCheckoutSessionResponse = (CheckoutSessionResponse);

export type SubscriptionsGetUserPaymentMethodsData = {
    paymentType?: string;
};

export type SubscriptionsGetUserPaymentMethodsResponse = (Array<PaymentMethodResponse>);

export type SubscriptionsGetUserInvoicesData = {
    limit?: number;
    status?: (string | null);
};

export type SubscriptionsGetUserInvoicesResponse = (Array<InvoiceResponse>);

export type SubscriptionsSubmitEnterpriseEnquiryData = {
    requestBody: EnterpriseEnquiryRequest;
};

export type SubscriptionsSubmitEnterpriseEnquiryResponse = (EnterpriseEnquiryMessageResponse);

export type SubscriptionsSubmitPlanChangeRequestData = {
    requestBody: PlanChangeRequestCreate;
};

export type SubscriptionsSubmitPlanChangeRequestResponse = (PlanChangeRequestResponse);

export type SubscriptionsWebhookResponse = (unknown);

export type SubscriptionsCancelSubscriptionResponse = ({
    [key: string]: unknown;
});

export type TasksCreateTaskData = {
    requestBody: TaskCreate;
};

export type TasksCreateTaskResponse = (TaskResponse);

export type TasksListTasksData = {
    executionStatus?: (TaskExecutionStatus | null);
    historyLimit?: number;
    includeHistory?: boolean;
    limit?: number;
    skip?: number;
};

export type TasksListTasksResponse = (TaskList);

export type TasksGetTaskData = {
    historyLimit?: number;
    includeHistory?: boolean;
    taskId: string;
};

export type TasksGetTaskResponse = (TaskResponse);

export type TasksUpdateTaskData = {
    requestBody: TaskUpdate;
    taskId: string;
};

export type TasksUpdateTaskResponse = (TaskResponse);

export type TasksDeleteTaskData = {
    taskId: string;
};

export type TasksDeleteTaskResponse = (TaskDeleteResponse);

export type TasksUpdateTaskEnableData = {
    enable?: boolean;
    taskId: string;
};

export type TasksUpdateTaskEnableResponse = (TaskResponse);

export type TasksStopTaskExecutionData = {
    conversationId: string;
    taskId: string;
};

export type TasksStopTaskExecutionResponse = (TaskStopResponse);

export type TasksGetTaskProgressData = {
    conversationId: string;
    taskId: string;
};

export type TasksGetTaskProgressResponse = (TaskExecutionStatus);

export type TasksContinueInterruptedTaskData = {
    requestBody: TaskContinueRequest;
    taskHistoryId: string;
};

export type TasksContinueInterruptedTaskResponse = (TaskContinueResponse);

export type TaskTemplatesGenerateData = {
    input: string;
};

export type TaskTemplatesGenerateResponse = (TaskTemplateResponse);

export type TaskTemplatesCreateTemplateData = {
    isDefault?: boolean;
    requestBody: TaskTemplateCreate;
};

export type TaskTemplatesCreateTemplateResponse = (TaskTemplateResponse);

export type TaskTemplatesListTemplatesData = {
    category?: (Array<TaskCategoryEnum> | null);
    includeDefaults?: boolean;
    limit?: number;
    searchQuery?: (string | null);
    services?: (Array<TaskServiceEnum> | null);
    skip?: number;
};

export type TaskTemplatesListTemplatesResponse = (TaskTemplateList);

export type TaskTemplatesGetTemplateData = {
    templateId: string;
};

export type TaskTemplatesGetTemplateResponse = (TaskTemplateResponse);

export type TaskTemplatesUpdateTemplateData = {
    requestBody: TaskTemplateUpdate;
    templateId: string;
};

export type TaskTemplatesUpdateTemplateResponse = (TaskTemplateResponse);

export type TaskTemplatesDeleteTemplateData = {
    templateId: string;
};

export type TaskTemplatesDeleteTemplateResponse = (unknown);

export type ToolsScriptExecutionData = {
    script: string;
};

export type ToolsScriptExecutionResponse = (ScriptExecutionResponse);

export type UsersReadUsersData = {
    limit?: number;
    skip?: number;
};

export type UsersReadUsersResponse = (UsersPublic);

export type UsersCreateUserData = {
    requestBody: UserCreate;
};

export type UsersCreateUserResponse = (UserPublic);

export type UsersReadUserMeResponse = (UserDetail);

export type UsersDeleteUserMeResponse = (Message);

export type UsersUpdateUserMeData = {
    requestBody: UserUpdateMe;
};

export type UsersUpdateUserMeResponse = (UserPublic);

export type UsersUpdatePasswordMeData = {
    requestBody: UpdatePassword;
};

export type UsersUpdatePasswordMeResponse = (Message);

export type UsersReadUserByIdData = {
    userId: string;
};

export type UsersReadUserByIdResponse = (UserPublic);

export type UsersUpdateUserData = {
    requestBody: UserUpdate;
    userId: string;
};

export type UsersUpdateUserResponse = (UserPublic);

export type UsersDeleteUserData = {
    userId: string;
};

export type UsersDeleteUserResponse = (Message);

export type UsersSwitchWorkspaceData = {
    workspaceId: string;
};

export type UsersSwitchWorkspaceResponse = (Token);

export type UtilsTestEmailData = {
    emailTo: string;
};

export type UtilsTestEmailResponse = (unknown);

export type UtilsHealthCheckResponse = (boolean);

export type UtilsPublishMessageData = {
    requestBody: Message;
};

export type UtilsPublishMessageResponse = (unknown);

export type UtilsEnqueueMessageData = {
    requestBody: Message;
    taskName: string;
};

export type UtilsEnqueueMessageResponse = (unknown);

export type WorkflowsReadWorkflowsData = {
    limit?: number;
    skip?: number;
};

export type WorkflowsReadWorkflowsResponse = (WorkflowsPublic);

export type WorkflowsCreateWorkflowData = {
    requestBody: WorkflowCreate;
};

export type WorkflowsCreateWorkflowResponse = (WorkflowPublic);

export type WorkflowsReadWorkflowData = {
    id: string;
};

export type WorkflowsReadWorkflowResponse = (WorkflowPublic);

export type WorkflowsUpdateWorkflowData = {
    id: string;
    requestBody: WorkflowUpdate;
};

export type WorkflowsUpdateWorkflowResponse = (WorkflowPublic);

export type WorkflowsDeleteWorkflowData = {
    id: string;
};

export type WorkflowsDeleteWorkflowResponse = (Message);

export type WorkflowsCreateWorkflowFromTemplateData = {
    workspaceId: string;
};

export type WorkflowsCreateWorkflowFromTemplateResponse = (WorkflowPublic);

export type WorkflowsCreateWorkflowNodeData = {
    requestBody: WorkflowNodeCreate;
    workflowId: string;
};

export type WorkflowsCreateWorkflowNodeResponse = (WorkflowNodePublic);

export type WorkflowsReadWorkflowNodesData = {
    limit?: number;
    skip?: number;
    workflowId: string;
};

export type WorkflowsReadWorkflowNodesResponse = (Array<WorkflowNodePublic>);

export type WorkflowsReadWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type WorkflowsReadWorkflowNodeResponse = (WorkflowNodePublic);

export type WorkflowsUpdateWorkflowNodeData = {
    nodeId: string;
    requestBody: WorkflowNodeUpdate;
    workflowId: string;
};

export type WorkflowsUpdateWorkflowNodeResponse = (WorkflowNodePublic);

export type WorkflowsDeleteWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type WorkflowsDeleteWorkflowNodeResponse = (Message);

export type WorkflowsRunWorkflowNodeData = {
    nodeId: string;
    workflowId: string;
};

export type WorkflowsRunWorkflowNodeResponse = (Message);

export type WorkflowsRunWorkflowData = {
    workflowId: string;
};

export type WorkflowsRunWorkflowResponse = (Message);

export type WorkspacesReadWorkspacesData = {
    limit?: number;
    skip?: number;
};

export type WorkspacesReadWorkspacesResponse = (WorkspacesPublic);

export type WorkspacesCreateWorkspaceData = {
    requestBody: WorkspaceCreate;
};

export type WorkspacesCreateWorkspaceResponse = (WorkspacePublic);

export type WorkspacesReadWorkspaceData = {
    id: string;
};

export type WorkspacesReadWorkspaceResponse = (WorkspaceDetail);

export type WorkspacesUpdateWorkspaceData = {
    id: string;
    requestBody: WorkspaceUpdate;
};

export type WorkspacesUpdateWorkspaceResponse = (WorkspacePublic);

export type WorkspacesDeleteWorkspaceData = {
    id: string;
};

export type WorkspacesDeleteWorkspaceResponse = (Message);
