// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from './core/CancelablePromise';
import { OpenAPI } from './core/OpenAPI';
import { request as __request } from './core/request';
import type { AgentConnectorsCreateAgentConnectorData, AgentConnectorsCreateAgentConnectorResponse, AgentConnectorsGetAgentConnectorData, AgentConnectorsGetAgentConnectorResponse, AgentConnectorsUpdateAgentConnectorData, AgentConnectorsUpdateAgentConnectorResponse, AgentConnectorsDeleteAgentConnectorData, AgentConnectorsDeleteAgentConnectorResponse, AgentConnectorsGetAgentConnectorsByWorkspaceIdResponse, AgentContextUpdateAgentContextData, AgentContextUpdateAgentContextResponse, AgentContextGetAgentContextData, AgentContextGetAgentContextResponse, AgentContextGetAgentContextsData, AgentContextGetAgentContextsResponse, AgentsReadAgentsData, AgentsReadAgentsResponse, AgentsCreateAgentData, AgentsCreateAgentResponse, AgentsReadAgentData, AgentsReadAgentResponse, AgentsUpdateAgentData, AgentsUpdateAgentResponse, AgentsDeleteAgentData, AgentsDeleteAgentResponse, AgentsInitDefaultAgentsData, AgentsInitDefaultAgentsResponse, AlertsGetAlertStatusSummaryResponse, AlertsCreateAlertData, AlertsCreateAlertResponse, AlertsListAlertsData, AlertsListAlertsResponse, AlertsGetAlertData, AlertsGetAlertResponse, AlertsUpdateAlertData, AlertsUpdateAlertResponse, AlertsDeleteAlertData, AlertsDeleteAlertResponse, AlertsUpdateAlertStatusData, AlertsUpdateAlertStatusResponse, AlertsMarkAllAlertsAcknowledgedResponse, AttachmentsGenerateAttachmentPresignedUrlsData, AttachmentsGenerateAttachmentPresignedUrlsResponse, AttachmentsConfirmAttachmentUploadsData, AttachmentsConfirmAttachmentUploadsResponse, AttachmentsGetAttachmentTaskStatusData, AttachmentsGetAttachmentTaskStatusResponse, AttachmentsGetAttachmentDownloadUrlData, AttachmentsGetAttachmentDownloadUrlResponse, AttachmentsGetAttachmentMetadataData, AttachmentsGetAttachmentMetadataResponse, AuthRegisterData, AuthRegisterResponse, AuthActivateAccountData, AuthActivateAccountResponse, AuthResendActivationData, AuthResendActivationResponse, AutonomousAgentsCreateConversationData, AutonomousAgentsCreateConversationResponse, AutonomousAgentsGetConversationsData, AutonomousAgentsGetConversationsResponse, AutonomousAgentsGetMessagesHistoryData, AutonomousAgentsGetMessagesHistoryResponse, AutonomousAgentsChatStreamData, AutonomousAgentsChatStreamResponse, AutonomousAgentsRenameConversationData, AutonomousAgentsRenameConversationResponse, AutonomousAgentsDeleteConversationData, AutonomousAgentsDeleteConversationResponse, AwsAccountsReadAwsAccountsData, AwsAccountsReadAwsAccountsResponse, AwsAccountsCreateAwsAccountData, AwsAccountsCreateAwsAccountResponse, AwsAccountsReadAwsAccountData, AwsAccountsReadAwsAccountResponse, AwsAccountsUpdateAwsAccountData, AwsAccountsUpdateAwsAccountResponse, AwsAccountsDeleteAwsAccountData, AwsAccountsDeleteAwsAccountResponse, BuiltinConnectorsListWorkspaceConnectorsData, BuiltinConnectorsListWorkspaceConnectorsResponse, BuiltinConnectorsUpdateConnectorForWorkspaceData, BuiltinConnectorsUpdateConnectorForWorkspaceResponse, BuiltinConnectorsUpdateConnectorPermissionData, BuiltinConnectorsUpdateConnectorPermissionResponse, ConnectorsCreateConnectorData, ConnectorsCreateConnectorResponse, ConnectorsListConnectorsData, ConnectorsListConnectorsResponse, ConnectorsGetConnectorData, ConnectorsGetConnectorResponse, ConnectorsUpdateConnectorData, ConnectorsUpdateConnectorResponse, ConnectorsDeleteConnectorData, ConnectorsDeleteConnectorResponse, ConsoleProxyGetFilesResponse, ConsoleProxyGetFileContentData, ConsoleProxyGetFileContentResponse, FilesCreateUploadUrlData, FilesCreateUploadUrlResponse, FilesCheckUploadStatusData, FilesCheckUploadStatusResponse, GoogleGoogleLoginResponse, GoogleGoogleCallbackResponse, ItemsReadItemsData, ItemsReadItemsResponse, ItemsCreateItemData, ItemsCreateItemResponse, ItemsReadItemData, ItemsReadItemResponse, ItemsUpdateItemData, ItemsUpdateItemResponse, ItemsDeleteItemData, ItemsDeleteItemResponse, KnowledgeBaseCreateKbData, KnowledgeBaseCreateKbResponse, KnowledgeBaseGetKbsData, KnowledgeBaseGetKbsResponse, KnowledgeBaseGetAvailableUsersResponse, KnowledgeBaseGetPointUsageResponse, KnowledgeBaseGetKbByIdData, KnowledgeBaseGetKbByIdResponse, KnowledgeBaseUpdateKbData, KnowledgeBaseUpdateKbResponse, KnowledgeBaseDeleteKbData, KnowledgeBaseDeleteKbResponse, KnowledgeBaseGeneratePresignedUrlsData, KnowledgeBaseGeneratePresignedUrlsResponse, KnowledgeBaseConfirmFileUploadsData, KnowledgeBaseConfirmFileUploadsResponse, KnowledgeBaseUploadUrlsData, KnowledgeBaseUploadUrlsResponse, KnowledgeBaseListDocumentsData, KnowledgeBaseListDocumentsResponse, KnowledgeBaseGetDocumentContentData, KnowledgeBaseGetDocumentContentResponse, KnowledgeBaseDeleteDocumentData, KnowledgeBaseDeleteDocumentResponse, KnowledgeBaseGetTaskStatusData, KnowledgeBaseGetTaskStatusResponse, KnowledgeBaseRuntimeSearchData, KnowledgeBaseRuntimeSearchResponse, KnowledgeBaseRuntimeSummarizeData, KnowledgeBaseRuntimeSummarizeResponse, LoginLoginAccessTokenData, LoginLoginAccessTokenResponse, LoginTestTokenResponse, LoginRecoverPasswordData, LoginRecoverPasswordResponse, LoginResetPasswordData, LoginResetPasswordResponse, LoginRecoverPasswordHtmlContentData, LoginRecoverPasswordHtmlContentResponse, McpServerGetMcpServersResponse, McpServerCreateMcpServerData, McpServerCreateMcpServerResponse, McpServerGetMcpServerData, McpServerGetMcpServerResponse, McpServerUpdateMcpServerData, McpServerUpdateMcpServerResponse, McpServerDeleteMcpServerData, McpServerDeleteMcpServerResponse, McpServerRefreshMcpServerData, McpServerRefreshMcpServerResponse, MemoryGetMemoryData, MemoryGetMemoryResponse, MemoryDeleteMemoryData, MemoryDeleteMemoryResponse, MemoryUpdateMemoryData, MemoryUpdateMemoryResponse, MessageFeedbackGetMessageFeedbackData, MessageFeedbackGetMessageFeedbackResponse, MessageFeedbackUpdateMessageFeedbackData, MessageFeedbackUpdateMessageFeedbackResponse, MessageFeedbackDeleteMessageFeedbackData, MessageFeedbackDeleteMessageFeedbackResponse, MessageFeedbackCreateMessageFeedbackData, MessageFeedbackCreateMessageFeedbackResponse, MetricsReadMetricsData, MetricsReadMetricsResponse, MetricsCreateMetricData, MetricsCreateMetricResponse, MetricsReadMetricData, MetricsReadMetricResponse, MetricsUpdateMetricData, MetricsUpdateMetricResponse, MetricsDeleteMetricData, MetricsDeleteMetricResponse, ModuleSettingGetModuleSettingsResponse, NotificationsListNotificationsData, NotificationsListNotificationsResponse, NotificationsMarkNotificationReadData, NotificationsMarkNotificationReadResponse, NotificationsMarkAllNotificationsReadData, NotificationsMarkAllNotificationsReadResponse, QuotasCreateUsageData, QuotasCreateUsageResponse, QuotasGetMessagesStatisticsData, QuotasGetMessagesStatisticsResponse, QuotasCreateUsageQuotaData, QuotasCreateUsageQuotaResponse, QuotasGetUsageQuotaData, QuotasGetUsageQuotaResponse, QuotasResetUserQuotaData, QuotasResetUserQuotaResponse, QuotasGetUsageStatisticsData, QuotasGetUsageStatisticsResponse, QuotasGetQuotaInfoData, QuotasGetQuotaInfoResponse, RecommendationsGetRecomendationOveralResponse, RecommendationsReadRecommendationsData, RecommendationsReadRecommendationsResponse, RecommendationsCreateRecommendationData, RecommendationsCreateRecommendationResponse, RecommendationsReadRecommendationData, RecommendationsReadRecommendationResponse, RecommendationsUpdateRecommendationData, RecommendationsUpdateRecommendationResponse, RecommendationsDeleteRecommendationData, RecommendationsDeleteRecommendationResponse, RecommendationsUpdateRecommendationStatusData, RecommendationsUpdateRecommendationStatusResponse, ReportsGetSavingsSummaryData, ReportsGetSavingsSummaryResponse, ReportsGetSavingsByResourceData, ReportsGetSavingsByResourceResponse, ReportsGetTopPotentialSavingsData, ReportsGetTopPotentialSavingsResponse, ReportsGetSavingsByServiceData, ReportsGetSavingsByServiceResponse, ReportsGetReportByConversationData, ReportsGetReportByConversationResponse, ResourcesReadResourcesData, ResourcesReadResourcesResponse, ResourcesCreateResourceData, ResourcesCreateResourceResponse, ResourcesReadResourceData, ResourcesReadResourceResponse, ResourcesUpdateResourceData, ResourcesUpdateResourceResponse, ResourcesDeleteResourceData, ResourcesDeleteResourceResponse, SampleDataCreateSampleResourcesData, SampleDataCreateSampleResourcesResponse, SampleDataCreateSampleMetricsData, SampleDataCreateSampleMetricsResponse, SampleDataCreateSampleRecommendationsData, SampleDataCreateSampleRecommendationsResponse, ShareChatCreateShareLinkData, ShareChatCreateShareLinkResponse, ShareChatRevokeShareLinkData, ShareChatRevokeShareLinkResponse, ShareChatGetShareLinkData, ShareChatGetShareLinkResponse, ShareChatGetSharedConversationData, ShareChatGetSharedConversationResponse, SubscriptionsGetAvailablePlansResponse, SubscriptionsGetUserSubscriptionStatusResponse, SubscriptionsGetWorkspaceSubscriptionStatusData, SubscriptionsGetWorkspaceSubscriptionStatusResponse, SubscriptionsCreateCheckoutSessionData, SubscriptionsCreateCheckoutSessionResponse, SubscriptionsGetUserPaymentMethodsData, SubscriptionsGetUserPaymentMethodsResponse, SubscriptionsGetUserInvoicesData, SubscriptionsGetUserInvoicesResponse, SubscriptionsSubmitEnterpriseEnquiryData, SubscriptionsSubmitEnterpriseEnquiryResponse, SubscriptionsSubmitPlanChangeRequestData, SubscriptionsSubmitPlanChangeRequestResponse, SubscriptionsWebhookResponse, SubscriptionsCancelSubscriptionResponse, TasksCreateTaskData, TasksCreateTaskResponse, TasksListTasksData, TasksListTasksResponse, TasksGetTaskData, TasksGetTaskResponse, TasksUpdateTaskData, TasksUpdateTaskResponse, TasksDeleteTaskData, TasksDeleteTaskResponse, TasksUpdateTaskEnableData, TasksUpdateTaskEnableResponse, TasksStopTaskExecutionData, TasksStopTaskExecutionResponse, TasksGetTaskProgressData, TasksGetTaskProgressResponse, TasksContinueInterruptedTaskData, TasksContinueInterruptedTaskResponse, TaskTemplatesGenerateData, TaskTemplatesGenerateResponse, TaskTemplatesCreateTemplateData, TaskTemplatesCreateTemplateResponse, TaskTemplatesListTemplatesData, TaskTemplatesListTemplatesResponse, TaskTemplatesGetTemplateData, TaskTemplatesGetTemplateResponse, TaskTemplatesUpdateTemplateData, TaskTemplatesUpdateTemplateResponse, TaskTemplatesDeleteTemplateData, TaskTemplatesDeleteTemplateResponse, ToolsScriptExecutionData, ToolsScriptExecutionResponse, UsersReadUsersData, UsersReadUsersResponse, UsersCreateUserData, UsersCreateUserResponse, UsersReadUserMeResponse, UsersDeleteUserMeResponse, UsersUpdateUserMeData, UsersUpdateUserMeResponse, UsersUpdatePasswordMeData, UsersUpdatePasswordMeResponse, UsersReadUserByIdData, UsersReadUserByIdResponse, UsersUpdateUserData, UsersUpdateUserResponse, UsersDeleteUserData, UsersDeleteUserResponse, UsersSwitchWorkspaceData, UsersSwitchWorkspaceResponse, UtilsTestEmailData, UtilsTestEmailResponse, UtilsHealthCheckResponse, UtilsPublishMessageData, UtilsPublishMessageResponse, UtilsEnqueueMessageData, UtilsEnqueueMessageResponse, WorkflowsReadWorkflowsData, WorkflowsReadWorkflowsResponse, WorkflowsCreateWorkflowData, WorkflowsCreateWorkflowResponse, WorkflowsReadWorkflowData, WorkflowsReadWorkflowResponse, WorkflowsUpdateWorkflowData, WorkflowsUpdateWorkflowResponse, WorkflowsDeleteWorkflowData, WorkflowsDeleteWorkflowResponse, WorkflowsCreateWorkflowFromTemplateData, WorkflowsCreateWorkflowFromTemplateResponse, WorkflowsCreateWorkflowNodeData, WorkflowsCreateWorkflowNodeResponse, WorkflowsReadWorkflowNodesData, WorkflowsReadWorkflowNodesResponse, WorkflowsReadWorkflowNodeData, WorkflowsReadWorkflowNodeResponse, WorkflowsUpdateWorkflowNodeData, WorkflowsUpdateWorkflowNodeResponse, WorkflowsDeleteWorkflowNodeData, WorkflowsDeleteWorkflowNodeResponse, WorkflowsRunWorkflowNodeData, WorkflowsRunWorkflowNodeResponse, WorkflowsRunWorkflowData, WorkflowsRunWorkflowResponse, WorkspacesReadWorkspacesData, WorkspacesReadWorkspacesResponse, WorkspacesCreateWorkspaceData, WorkspacesCreateWorkspaceResponse, WorkspacesReadWorkspaceData, WorkspacesReadWorkspaceResponse, WorkspacesUpdateWorkspaceData, WorkspacesUpdateWorkspaceResponse, WorkspacesDeleteWorkspaceData, WorkspacesDeleteWorkspaceResponse } from './types.gen';

export class AgentConnectorsService {
    /**
     * Create Agent Connector
     * Create a new agent connector.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static createAgentConnector(data: AgentConnectorsCreateAgentConnectorData): CancelablePromise<AgentConnectorsCreateAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agent-connectors',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Connector
     * Get an agent connector by agent ID.
     * @param data The data for the request.
     * @param data.agentId
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getAgentConnector(data: AgentConnectorsGetAgentConnectorData): CancelablePromise<AgentConnectorsGetAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Agent Connector
     * Update an existing agent connector.
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static updateAgentConnector(data: AgentConnectorsUpdateAgentConnectorData): CancelablePromise<AgentConnectorsUpdateAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Agent Connector
     * Delete an agent connector.
     * @param data The data for the request.
     * @param data.agentId
     * @returns void Successful Response
     * @throws ApiError
     */
    public static deleteAgentConnector(data: AgentConnectorsDeleteAgentConnectorData): CancelablePromise<AgentConnectorsDeleteAgentConnectorResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/agent-connectors/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Connectors By Workspace Id
     * Get all agent connectors by workspace ID.
     * @returns AgentConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getAgentConnectorsByWorkspaceId(): CancelablePromise<AgentConnectorsGetAgentConnectorsByWorkspaceIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-connectors/'
        });
    }

}

export class AgentContextService {
    /**
     * Update Agent Context
     * Update agent context
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentContextRead Successful Response
     * @throws ApiError
     */
    public static updateAgentContext(data: AgentContextUpdateAgentContextData): CancelablePromise<AgentContextUpdateAgentContextResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agent-context/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Context
     * Get context based on agent id
     * @param data The data for the request.
     * @param data.agentId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getAgentContext(data: AgentContextGetAgentContextData): CancelablePromise<AgentContextGetAgentContextResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-context/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Agent Contexts
     * Get contexts based on a list of agent ids, for each agent id, get the latest none deleted context
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentContextListResponse Successful Response
     * @throws ApiError
     */
    public static getAgentContexts(data: AgentContextGetAgentContextsData): CancelablePromise<AgentContextGetAgentContextsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agent-context/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AgentsService {
    /**
     * Read Agents
     * Retrieve Agents for the current workspace.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns AgentsPublic Successful Response
     * @throws ApiError
     */
    public static readAgents(data: AgentsReadAgentsData = {}): CancelablePromise<AgentsReadAgentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Agent
     * Create new Agent.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static createAgent(data: AgentsCreateAgentData): CancelablePromise<AgentsCreateAgentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agents/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Agent
     * Get Agent by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static readAgent(data: AgentsReadAgentData): CancelablePromise<AgentsReadAgentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Agent
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static updateAgent(data: AgentsUpdateAgentData): CancelablePromise<AgentsUpdateAgentResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Agent
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteAgent(data: AgentsDeleteAgentData): CancelablePromise<AgentsDeleteAgentResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/agents/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Init Default Agents
     * Initialize default agents for a workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static initDefaultAgents(data: AgentsInitDefaultAgentsData): CancelablePromise<AgentsInitDefaultAgentsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agents/init-default/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AlertsService {
    /**
     * Get Alert Status Summary
     * Get a summary of alerts by status for the last 30 days.
     * @returns AlertStatusSummary Successful Response
     * @throws ApiError
     */
    public static getAlertStatusSummary(): CancelablePromise<AlertsGetAlertStatusSummaryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/summary/status'
        });
    }

    /**
     * Create Alert
     * Create new alert.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static createAlert(data: AlertsCreateAlertData): CancelablePromise<AlertsCreateAlertResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/alerts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Alerts
     * List alerts with optional filters.
     * @param data The data for the request.
     * @param data.severity
     * @param data.status
     * @param data.sortBy Field to sort by
     * @param data.sortDesc Sort in descending order
     * @param data.skip
     * @param data.limit
     * @returns AlertList Successful Response
     * @throws ApiError
     */
    public static listAlerts(data: AlertsListAlertsData = {}): CancelablePromise<AlertsListAlertsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/',
            query: {
                severity: data.severity,
                status: data.status,
                sort_by: data.sortBy,
                sort_desc: data.sortDesc,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Alert
     * Get alert by ID.
     * @param data The data for the request.
     * @param data.alertId
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static getAlert(data: AlertsGetAlertData): CancelablePromise<AlertsGetAlertResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Alert
     * Update alert.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlert(data: AlertsUpdateAlertData): CancelablePromise<AlertsUpdateAlertResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Alert
     * Delete alert.
     * @param data The data for the request.
     * @param data.alertId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteAlert(data: AlertsDeleteAlertData): CancelablePromise<AlertsDeleteAlertResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Alert Status
     * Update alert status.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.status
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlertStatus(data: AlertsUpdateAlertStatusData): CancelablePromise<AlertsUpdateAlertStatusResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/alerts/{alert_id}/status',
            path: {
                alert_id: data.alertId
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Mark All Alerts Acknowledged
     * Mark all alerts as acknowledged for the current workspace.
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static markAllAlertsAcknowledged(): CancelablePromise<AlertsMarkAllAlertsAcknowledgedResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/alerts/mark-all-acknowledged'
        });
    }

}

export class AttachmentsService {
    /**
     * Generate Attachment Presigned Urls
     * Generate presigned URLs for file uploads.
     * Clients can use these URLs to upload files directly to the object storage.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AttachmentPresignedUrlResponse Successful Response
     * @throws ApiError
     */
    public static generateAttachmentPresignedUrls(data: AttachmentsGenerateAttachmentPresignedUrlsData): CancelablePromise<AttachmentsGenerateAttachmentPresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/attachments/attachments/presigned-urls',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Confirm Attachment Uploads
     * Confirm that files have been uploaded and trigger the validation task.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns app__schemas__message_attachment__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static confirmAttachmentUploads(data: AttachmentsConfirmAttachmentUploadsData): CancelablePromise<AttachmentsConfirmAttachmentUploadsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/attachments/attachments/confirm-uploads',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Attachment Task Status
     * Get the status of an asynchronous attachment validation task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns app__schemas__message_attachment__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentTaskStatus(data: AttachmentsGetAttachmentTaskStatusData): CancelablePromise<AttachmentsGetAttachmentTaskStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Attachment Download Url
     * Generate a presigned GET URL to download an attachment.
     * @param data The data for the request.
     * @param data.attachmentId
     * @returns AttachmentDownloadResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentDownloadUrl(data: AttachmentsGetAttachmentDownloadUrlData): CancelablePromise<AttachmentsGetAttachmentDownloadUrlResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/{attachment_id}/download-url',
            path: {
                attachment_id: data.attachmentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Attachment Metadata
     * Get attachment metadata by attachment ID.
     * @param data The data for the request.
     * @param data.attachmentId
     * @returns AttachmentMetadataResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentMetadata(data: AttachmentsGetAttachmentMetadataData): CancelablePromise<AttachmentsGetAttachmentMetadataResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/{attachment_id}/metadata',
            path: {
                attachment_id: data.attachmentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AuthService {
    /**
     * Register
     * Register new user and send activation email.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static register(data: AuthRegisterData): CancelablePromise<AuthRegisterResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/signup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Activate Account
     * Activate a user account using the activation token.
     * @param data The data for the request.
     * @param data.token
     * @returns ActivationResult Successful Response
     * @throws ApiError
     */
    public static activateAccount(data: AuthActivateAccountData): CancelablePromise<AuthActivateAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/activate/{token}',
            path: {
                token: data.token
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Resend Activation
     * Resend activation email for unactivated accounts with reCAPTCHA v3 validation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static resendActivation(data: AuthResendActivationData): CancelablePromise<AuthResendActivationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/resend-activation',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AutonomousAgentsService {
    /**
     * Create Conversation
     * Create a new conversation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ConversationPublic Successful Response
     * @throws ApiError
     */
    public static createConversation(data: AutonomousAgentsCreateConversationData): CancelablePromise<AutonomousAgentsCreateConversationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/conversations',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Conversations
     * Get list of conversations with filtering and pagination.
     *
     * Args:
     * current_user: The authenticated user
     * session: Database session
     * agent_id: Optional agent ID to filter by
     * resource_id: Optional resource ID to filter by
     * model_provider: Model provider to use (defaults to 'bedrock')
     * skip: Number of records to skip for pagination
     * limit: Maximum number of records to return (1-100)
     *
     * Returns:
     * ConversationsPublic: Paginated list of conversations
     *
     * Raises:
     * HTTPException: If conversation not found or other error occurs
     * @param data The data for the request.
     * @param data.agentId
     * @param data.resourceId
     * @param data.modelProvider
     * @param data.skip Number of records to skip for pagination
     * @param data.limit Maximum number of records to return
     * @returns ConversationsPublic Successful Response
     * @throws ApiError
     */
    public static getConversations(data: AutonomousAgentsGetConversationsData = {}): CancelablePromise<AutonomousAgentsGetConversationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/conversations',
            query: {
                agent_id: data.agentId,
                resource_id: data.resourceId,
                model_provider: data.modelProvider,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Messages History
     * Get message history for a conversation.
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.limit
     * @returns MessageHistoryPublic Successful Response
     * @throws ApiError
     */
    public static getMessagesHistory(data: AutonomousAgentsGetMessagesHistoryData): CancelablePromise<AutonomousAgentsGetMessagesHistoryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/messages/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            query: {
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Chat Stream
     * Stream chat responses from the agent.
     *
     * Args:
     * conversation_id: ID of the conversation
     * message: User message with content and resume flag
     * session: Database session
     *
     * Returns:
     * StreamingResponse: Server-sent events stream of agent responses
     *
     * Raises:
     * HTTPException: If conversation not found (404) or no previous message found (404)
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.requestBody
     * @returns StreamResponse Successful Response
     * @throws ApiError
     */
    public static chatStream(data: AutonomousAgentsChatStreamData): CancelablePromise<AutonomousAgentsChatStreamResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/chat/{conversation_id}/stream',
            path: {
                conversation_id: data.conversationId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Rename Conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.name
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static renameConversation(data: AutonomousAgentsRenameConversationData): CancelablePromise<AutonomousAgentsRenameConversationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}/name',
            path: {
                conversation_id: data.conversationId
            },
            query: {
                name: data.name
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Conversation
     * Delete a conversation and its associated LangGraph thread data.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteConversation(data: AutonomousAgentsDeleteConversationData): CancelablePromise<AutonomousAgentsDeleteConversationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class AwsAccountsService {
    /**
     * Read Aws Accounts
     * Retrieve AWS accounts.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns AWSAccountsPublic Successful Response
     * @throws ApiError
     */
    public static readAwsAccounts(data: AwsAccountsReadAwsAccountsData = {}): CancelablePromise<AwsAccountsReadAwsAccountsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/aws-accounts/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Aws Account
     * Create new AWS account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static createAwsAccount(data: AwsAccountsCreateAwsAccountData): CancelablePromise<AwsAccountsCreateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/aws-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Aws Account
     * Get AWS account by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static readAwsAccount(data: AwsAccountsReadAwsAccountData): CancelablePromise<AwsAccountsReadAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Aws Account
     * Update an AWS account.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static updateAwsAccount(data: AwsAccountsUpdateAwsAccountData): CancelablePromise<AwsAccountsUpdateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Aws Account
     * Delete an AWS account.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteAwsAccount(data: AwsAccountsDeleteAwsAccountData): CancelablePromise<AwsAccountsDeleteAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/aws-accounts/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class BuiltinConnectorsService {
    /**
     * List Workspace Connectors
     * List all built-in connectors for a workspace
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.activeOnly
     * @returns ConnectorWithStatusResponse Successful Response
     * @throws ApiError
     */
    public static listWorkspaceConnectors(data: BuiltinConnectorsListWorkspaceConnectorsData = {}): CancelablePromise<BuiltinConnectorsListWorkspaceConnectorsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/builtin-connectors/',
            query: {
                skip: data.skip,
                limit: data.limit,
                active_only: data.activeOnly
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector For Workspace
     * Update the active status of a built-in connector for a workspace
     * @param data The data for the request.
     * @param data.connectorId
     * @param data.isActive
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static updateConnectorForWorkspace(data: BuiltinConnectorsUpdateConnectorForWorkspaceData): CancelablePromise<BuiltinConnectorsUpdateConnectorForWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/builtin-connectors/{connector_id}/',
            path: {
                connector_id: data.connectorId
            },
            query: {
                is_active: data.isActive
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector Permission
     * Update whether a tool requires human approval before execution.
     *
     * Args:
     * workspace_id: ID of the workspace
     * connector_id: ID of the connector
     * required_permission: Whether to require human approval for this tool
     * session: Database session
     *
     * Returns:
     * bool: True if the permission requirement was updated successfully
     * @param data The data for the request.
     * @param data.connectorId
     * @param data.requiredPermission
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static updateConnectorPermission(data: BuiltinConnectorsUpdateConnectorPermissionData): CancelablePromise<BuiltinConnectorsUpdateConnectorPermissionResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/builtin-connectors/{connector_id}/permission',
            path: {
                connector_id: data.connectorId
            },
            query: {
                required_permission: data.requiredPermission
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ConnectorsService {
    /**
     * Create Connector
     * Create new knowledge base.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static createConnector(data: ConnectorsCreateConnectorData): CancelablePromise<ConnectorsCreateConnectorResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/connectors/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Connectors
     * List knowledge bases.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns ConnectorList Successful Response
     * @throws ApiError
     */
    public static listConnectors(data: ConnectorsListConnectorsData = {}): CancelablePromise<ConnectorsListConnectorsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/connectors/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Connector
     * Get knowledge base by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static getConnector(data: ConnectorsGetConnectorData): CancelablePromise<ConnectorsGetConnectorResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Connector
     * Update knowledge base.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ConnectorResponse Successful Response
     * @throws ApiError
     */
    public static updateConnector(data: ConnectorsUpdateConnectorData): CancelablePromise<ConnectorsUpdateConnectorResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Connector
     * Delete an item.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteConnector(data: ConnectorsDeleteConnectorData): CancelablePromise<ConnectorsDeleteConnectorResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/connectors/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ConsoleProxyService {
    /**
     * Get Files
     * Get files for the current user's workspace by proxying to executor
     * @returns FileListResponse Successful Response
     * @throws ApiError
     */
    public static getFiles(): CancelablePromise<ConsoleProxyGetFilesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/console/files'
        });
    }

    /**
     * Get File Content
     * Get file content from executor for display in the file explorer
     * @param data The data for the request.
     * @param data.path File path
     * @returns FileContentResponse Successful Response
     * @throws ApiError
     */
    public static getFileContent(data: ConsoleProxyGetFileContentData): CancelablePromise<ConsoleProxyGetFileContentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/console/files/content',
            query: {
                path: data.path
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class FilesService {
    /**
     * Create Upload Url
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UploadResponse Successful Response
     * @throws ApiError
     */
    public static createUploadUrl(data: FilesCreateUploadUrlData): CancelablePromise<FilesCreateUploadUrlResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/files/create-url',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Check Upload Status
     * @param data The data for the request.
     * @param data.id
     * @returns UploadPublic Successful Response
     * @throws ApiError
     */
    public static checkUploadStatus(data: FilesCheckUploadStatusData): CancelablePromise<FilesCheckUploadStatusResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/files/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class GoogleService {
    /**
     * Google Login
     * Initiate Google OAuth login flow
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static googleLogin(): CancelablePromise<GoogleGoogleLoginResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/login'
        });
    }

    /**
     * Google Callback
     * Handle Google OAuth callback and login/create user
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static googleCallback(): CancelablePromise<GoogleGoogleCallbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/callback'
        });
    }

}

export class ItemsService {
    /**
     * Read Items
     * Retrieve items.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns ItemsPublic Successful Response
     * @throws ApiError
     */
    public static readItems(data: ItemsReadItemsData = {}): CancelablePromise<ItemsReadItemsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Item
     * Create new item.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static createItem(data: ItemsCreateItemData): CancelablePromise<ItemsCreateItemResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/items/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Item
     * Get item by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static readItem(data: ItemsReadItemData): CancelablePromise<ItemsReadItemResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Item
     * Update an item.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ItemPublic Successful Response
     * @throws ApiError
     */
    public static updateItem(data: ItemsUpdateItemData): CancelablePromise<ItemsUpdateItemResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Item
     * Delete an item.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteItem(data: ItemsDeleteItemData): CancelablePromise<ItemsDeleteItemResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/items/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class KnowledgeBaseService {
    /**
     * Create Kb
     * @param data The data for the request.
     * @param data.requestBody
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static createKb(data: KnowledgeBaseCreateKbData): CancelablePromise<KnowledgeBaseCreateKbResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Kbs
     * Get all knowledge bases for the current user
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.accessLevel
     * @param data.usageMode
     * @returns KBsRead Successful Response
     * @throws ApiError
     */
    public static getKbs(data: KnowledgeBaseGetKbsData = {}): CancelablePromise<KnowledgeBaseGetKbsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs',
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                access_level: data.accessLevel,
                usage_mode: data.usageMode
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Available Users
     * Get a list of users available for sharing knowledge bases within the current workspace.
     * Returns users that are in the same workspace as the current user.
     * @returns AvailableUsersCurrentWorkspace Successful Response
     * @throws ApiError
     */
    public static getAvailableUsers(): CancelablePromise<KnowledgeBaseGetAvailableUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/available-users'
        });
    }

    /**
     * Get Point Usage
     * Get user's point usage across all knowledge bases
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getPointUsage(): CancelablePromise<KnowledgeBaseGetPointUsageResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/point-usage'
        });
    }

    /**
     * Get Kb By Id
     * Get a specific knowledge base by ID
     * @param data The data for the request.
     * @param data.kbId
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static getKbById(data: KnowledgeBaseGetKbByIdData): CancelablePromise<KnowledgeBaseGetKbByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Kb
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns KBRead Successful Response
     * @throws ApiError
     */
    public static updateKb(data: KnowledgeBaseUpdateKbData): CancelablePromise<KnowledgeBaseUpdateKbResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Kb
     * @param data The data for the request.
     * @param data.kbId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteKb(data: KnowledgeBaseDeleteKbData): CancelablePromise<KnowledgeBaseDeleteKbResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Generate Presigned Urls
     * Generate presigned URLs for file uploads.
     *
     * This endpoint generates presigned URLs that clients can use to upload files
     * directly to S3, bypassing the backend for better performance and scalability.
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns PresignedUrlResponse Successful Response
     * @throws ApiError
     */
    public static generatePresignedUrls(data: KnowledgeBaseGeneratePresignedUrlsData): CancelablePromise<KnowledgeBaseGeneratePresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Confirm File Uploads
     * Confirm file uploads and start ingestion process.
     *
     * This endpoint should be called after files have been successfully uploaded
     * using the presigned URLs to start the document ingestion process.
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static confirmFileUploads(data: KnowledgeBaseConfirmFileUploadsData): CancelablePromise<KnowledgeBaseConfirmFileUploadsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/confirm-uploads',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Upload Urls
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static uploadUrls(data: KnowledgeBaseUploadUrlsData): CancelablePromise<KnowledgeBaseUploadUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Documents
     * List documents in a knowledge base.
     *
     * User must have access to the knowledge base (owner for personal knowledge bases,
     * workspace member for workspace knowledge bases).
     * @param data The data for the request.
     * @param data.kbId
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @returns DocumentsKBRead Successful Response
     * @throws ApiError
     */
    public static listDocuments(data: KnowledgeBaseListDocumentsData): CancelablePromise<KnowledgeBaseListDocumentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents',
            path: {
                kb_id: data.kbId
            },
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Document Content
     * @param data The data for the request.
     * @param data.kbId
     * @param data.objectName
     * @returns string Successful Response
     * @throws ApiError
     */
    public static getDocumentContent(data: KnowledgeBaseGetDocumentContentData): CancelablePromise<KnowledgeBaseGetDocumentContentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/content',
            path: {
                kb_id: data.kbId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Document
     * @param data The data for the request.
     * @param data.kbId
     * @param data.documentId
     * @param data.objectName
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteDocument(data: KnowledgeBaseDeleteDocumentData): CancelablePromise<KnowledgeBaseDeleteDocumentResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}',
            path: {
                kb_id: data.kbId,
                document_id: data.documentId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task Status
     * Get the status of an asynchronous task.
     *
     * This endpoint returns the current status and progress of a Celery task,
     * such as document ingestion.
     * @param data The data for the request.
     * @param data.taskId
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static getTaskStatus(data: KnowledgeBaseGetTaskStatusData): CancelablePromise<KnowledgeBaseGetTaskStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class KnowledgeBaseRuntimeService {
    /**
     * Search knowledge base
     * Search the knowledge base with various modes and filters
     * @param data The data for the request.
     * @param data.query
     * @param data.kbId
     * @param data.requestBody
     * @returns SearchResponse Successful Response
     * @throws ApiError
     */
    public static search(data: KnowledgeBaseRuntimeSearchData): CancelablePromise<KnowledgeBaseRuntimeSearchResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base_runtime/search',
            query: {
                query: data.query,
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                400: 'Bad Request',
                422: 'Validation Error',
                500: 'Internal Server Error'
            }
        });
    }

    /**
     * Generate summary from knowledge base
     * Search and summarize content from knowledge base
     * @param data The data for the request.
     * @param data.query
     * @param data.kbId
     * @param data.requestBody
     * @returns SummaryResponse Successful Response
     * @throws ApiError
     */
    public static summarize(data: KnowledgeBaseRuntimeSummarizeData): CancelablePromise<KnowledgeBaseRuntimeSummarizeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base_runtime/summarize',
            query: {
                query: data.query,
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                400: 'Bad Request',
                422: 'Validation Error',
                500: 'Internal Server Error'
            }
        });
    }

}

export class LoginService {
    /**
     * Login Access Token
     * OAuth2 compatible token login, get an access token for future requests
     * @param data The data for the request.
     * @param data.formData
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static loginAccessToken(data: LoginLoginAccessTokenData): CancelablePromise<LoginLoginAccessTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/access-token',
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Test Token
     * Test access token
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static testToken(): CancelablePromise<LoginTestTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/test-token'
        });
    }

    /**
     * Recover Password
     * Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static recoverPassword(data: LoginRecoverPasswordData): CancelablePromise<LoginRecoverPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Reset Password
     * Reset password
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static resetPassword(data: LoginResetPasswordData): CancelablePromise<LoginResetPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/reset-password/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Recover Password Html Content
     * HTML Content for Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns string Successful Response
     * @throws ApiError
     */
    public static recoverPasswordHtmlContent(data: LoginRecoverPasswordHtmlContentData): CancelablePromise<LoginRecoverPasswordHtmlContentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery-html-content/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class McpServerService {
    /**
     * Get Mcp Servers
     * Get all MCP servers for a workspace.
     *
     * Returns information about all configured MCP servers for the workspace,
     * including their connection status, available tools, and configuration details.
     * @returns MCPServerListResponseSchema Successful Response
     * @throws ApiError
     */
    public static getMcpServers(): CancelablePromise<McpServerGetMcpServersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mcp-server/'
        });
    }

    /**
     * Create Mcp Server
     * Create a new MCP server for a workspace.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MCPServerResponseSchema Successful Response
     * @throws ApiError
     */
    public static createMcpServer(data: McpServerCreateMcpServerData): CancelablePromise<McpServerCreateMcpServerResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-server/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Mcp Server
     * Get a specific MCP server by ID.
     * @param data The data for the request.
     * @param data.serverId
     * @returns MCPServerResponseSchema Successful Response
     * @throws ApiError
     */
    public static getMcpServer(data: McpServerGetMcpServerData): CancelablePromise<McpServerGetMcpServerResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mcp-server/{server_id}',
            path: {
                server_id: data.serverId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Mcp Server
     * Update an existing MCP server.
     * @param data The data for the request.
     * @param data.serverId
     * @param data.requestBody
     * @returns MCPServerResponseSchema Successful Response
     * @throws ApiError
     */
    public static updateMcpServer(data: McpServerUpdateMcpServerData): CancelablePromise<McpServerUpdateMcpServerResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/mcp-server/{server_id}',
            path: {
                server_id: data.serverId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Mcp Server
     * Delete an MCP server.
     * @param data The data for the request.
     * @param data.serverId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteMcpServer(data: McpServerDeleteMcpServerData): CancelablePromise<McpServerDeleteMcpServerResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/mcp-server/{server_id}',
            path: {
                server_id: data.serverId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Refresh Mcp Server
     * Refresh an MCP server.
     * @param data The data for the request.
     * @param data.serverId
     * @returns MCPServerResponseSchema Successful Response
     * @throws ApiError
     */
    public static refreshMcpServer(data: McpServerRefreshMcpServerData): CancelablePromise<McpServerRefreshMcpServerResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-server/{server_id}/refresh',
            path: {
                server_id: data.serverId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MemoryService {
    /**
     * Get Memory
     * Get all memories for given agent roles. If agent roles are not provided, get all memories for all agent roles.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MemorysRead Successful Response
     * @throws ApiError
     */
    public static getMemory(data: MemoryGetMemoryData): CancelablePromise<MemoryGetMemoryResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/memory/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Memory
     * @param data The data for the request.
     * @param data.id
     * @param data.agentRole
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMemory(data: MemoryDeleteMemoryData): CancelablePromise<MemoryDeleteMemoryResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/memory/',
            query: {
                id: data.id,
                agent_role: data.agentRole
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Memory
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static updateMemory(data: MemoryUpdateMemoryData): CancelablePromise<MemoryUpdateMemoryResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/memory/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MessageFeedbackService {
    /**
     * Get Message Feedback
     * Get feedback for a specific message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getMessageFeedback(data: MessageFeedbackGetMessageFeedbackData): CancelablePromise<MessageFeedbackGetMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Message Feedback
     * Update feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static updateMessageFeedback(data: MessageFeedbackUpdateMessageFeedbackData): CancelablePromise<MessageFeedbackUpdateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Message Feedback
     * Delete feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMessageFeedback(data: MessageFeedbackDeleteMessageFeedbackData): CancelablePromise<MessageFeedbackDeleteMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Message Feedback
     * Create feedback for a message.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static createMessageFeedback(data: MessageFeedbackCreateMessageFeedbackData): CancelablePromise<MessageFeedbackCreateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/message-feedback/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class MetricsService {
    /**
     * Read Metrics
     * Retrieve metrics.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.resourceId
     * @param data.startDate
     * @param data.endDate
     * @returns MetricsPublic Successful Response
     * @throws ApiError
     */
    public static readMetrics(data: MetricsReadMetricsData = {}): CancelablePromise<MetricsReadMetricsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/metrics/',
            query: {
                skip: data.skip,
                limit: data.limit,
                resource_id: data.resourceId,
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Metric
     * Create new metric.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static createMetric(data: MetricsCreateMetricData): CancelablePromise<MetricsCreateMetricResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/metrics/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Metric
     * Get metric by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static readMetric(data: MetricsReadMetricData): CancelablePromise<MetricsReadMetricResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Metric
     * Update a metric.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns MetricPublic Successful Response
     * @throws ApiError
     */
    public static updateMetric(data: MetricsUpdateMetricData): CancelablePromise<MetricsUpdateMetricResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Metric
     * Delete a metric.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteMetric(data: MetricsDeleteMetricData): CancelablePromise<MetricsDeleteMetricResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/metrics/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ModuleSettingService {
    /**
     * Get Module Settings
     * Retrieve all module settings.
     * @returns ModuleSetting Successful Response
     * @throws ApiError
     */
    public static getModuleSettings(): CancelablePromise<ModuleSettingGetModuleSettingsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/module_setting/'
        });
    }

}

export class NotificationsService {
    /**
     * List Notifications
     * @param data The data for the request.
     * @param data.requiresAction
     * @param data.timeframe
     * @param data.skip
     * @param data.limit
     * @param data.requestBody
     * @returns NotificationList Successful Response
     * @throws ApiError
     */
    public static listNotifications(data: NotificationsListNotificationsData = {}): CancelablePromise<NotificationsListNotificationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/notifications/',
            query: {
                requires_action: data.requiresAction,
                timeframe: data.timeframe,
                skip: data.skip,
                limit: data.limit
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Mark Notification Read
     * @param data The data for the request.
     * @param data.notificationId
     * @returns NotificationResponse Successful Response
     * @throws ApiError
     */
    public static markNotificationRead(data: NotificationsMarkNotificationReadData): CancelablePromise<NotificationsMarkNotificationReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/{notification_id}/mark-read',
            path: {
                notification_id: data.notificationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Mark All Notifications Read
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static markAllNotificationsRead(data: NotificationsMarkAllNotificationsReadData = {}): CancelablePromise<NotificationsMarkAllNotificationsReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/mark-all-read',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class QuotasService {
    /**
     * Create Usage
     * @param data The data for the request.
     * @param data.requestBody
     * @returns TokenUsageResponse Successful Response
     * @throws ApiError
     */
    public static createUsage(data: QuotasCreateUsageData): CancelablePromise<QuotasCreateUsageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/usage',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Messages Statistics
     * Get message usage statistics for a workspace.
     *
     * Args:
     * start_date: Optional start date for filtering
     * end_date: Optional end date for filtering
     *
     * Returns:
     * Message statistics including:
     * - Total messages and month-over-month change
     * - Average response time and month-over-month change
     * - Success rate and month-over-month change
     * - Average tokens per message (input/output)
     * - Daily message volume (30-day trend)
     * - Token distribution by message length
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @returns MessageStatistics Successful Response
     * @throws ApiError
     */
    public static getMessagesStatistics(data: QuotasGetMessagesStatisticsData = {}): CancelablePromise<QuotasGetMessagesStatisticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/message-statistics',
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Usage Quota
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static createUsageQuota(data: QuotasCreateUsageQuotaData): CancelablePromise<QuotasCreateUsageQuotaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Usage Quota
     * Get usage quota for a specific workspace.
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * Usage quota details
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static getUsageQuota(data: QuotasGetUsageQuotaData): CancelablePromise<QuotasGetUsageQuotaResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Reset User Quota
     * Reset usage quota for a user.
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * Reset usage quota details
     * @param data The data for the request.
     * @param data.userId
     * @returns UsageQuotaResponse Successful Response
     * @throws ApiError
     */
    public static resetUserQuota(data: QuotasResetUserQuotaData): CancelablePromise<QuotasResetUserQuotaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/quotas/{user_id}/reset',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Usage Statistics
     * Get token usage statistics for a workspace.
     *
     * Args:
     * user_id: ID of the user
     * start_date: Optional start date for filtering
     * end_date: Optional end date for filtering
     *
     * Returns:
     * Usage statistics
     * @param data The data for the request.
     * @param data.userId
     * @param data.startDate
     * @param data.endDate
     * @returns UsageStatistics Successful Response
     * @throws ApiError
     */
    public static getUsageStatistics(data: QuotasGetUsageStatisticsData): CancelablePromise<QuotasGetUsageStatisticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/statistics',
            path: {
                user_id: data.userId
            },
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Quota Info
     * Get quota information for a user.
     *
     * Args:
     * user_id: ID of the user
     * @param data The data for the request.
     * @param data.userId
     * @returns QuotaInfo Successful Response
     * @throws ApiError
     */
    public static getQuotaInfo(data: QuotasGetQuotaInfoData): CancelablePromise<QuotasGetQuotaInfoResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/quota-info',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class RecommendationsService {
    /**
     * Get Recomendation Overal
     * Get overal recommendation statistics.
     * @returns RecommendationOveralPublic Successful Response
     * @throws ApiError
     */
    public static getRecomendationOveral(): CancelablePromise<RecommendationsGetRecomendationOveralResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/overal'
        });
    }

    /**
     * Read Recommendations
     * Retrieve recommendations.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.resourceId
     * @param data.resourceType
     * @param data.recommendationType
     * @param data.status
     * @param data.startDate
     * @param data.endDate
     * @param data.orderBy
     * @param data.orderDirection
     * @returns RecommendationsPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendations(data: RecommendationsReadRecommendationsData = {}): CancelablePromise<RecommendationsReadRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/',
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                resource_id: data.resourceId,
                resource_type: data.resourceType,
                recommendation_type: data.recommendationType,
                status: data.status,
                start_date: data.startDate,
                end_date: data.endDate,
                order_by: data.orderBy,
                order_direction: data.orderDirection
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Recommendation
     * Create new recommendation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static createRecommendation(data: RecommendationsCreateRecommendationData): CancelablePromise<RecommendationsCreateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/recommendations/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Recommendation
     * Get recommendation by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendation(data: RecommendationsReadRecommendationData): CancelablePromise<RecommendationsReadRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Recommendation
     * Update a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendation(data: RecommendationsUpdateRecommendationData): CancelablePromise<RecommendationsUpdateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Recommendation
     * Delete a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteRecommendation(data: RecommendationsDeleteRecommendationData): CancelablePromise<RecommendationsDeleteRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Recommendation Status
     * Update the status of a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.status
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendationStatus(data: RecommendationsUpdateRecommendationStatusData): CancelablePromise<RecommendationsUpdateRecommendationStatusResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}/status',
            path: {
                id: data.id
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ReportsService {
    /**
     * Get Savings Summary
     * Get total potential savings for the workspace with comparison between two periods.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @param data.previousStartDate
     * @param data.previousEndDate
     * @returns SavingSummaryReport Successful Response
     * @throws ApiError
     */
    public static getSavingsSummary(data: ReportsGetSavingsSummaryData = {}): CancelablePromise<ReportsGetSavingsSummaryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/saving-summary',
            query: {
                start_date: data.startDate,
                end_date: data.endDate,
                previous_start_date: data.previousStartDate,
                previous_end_date: data.previousEndDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Savings By Resource
     * Get savings data grouped by date for RDS and EC2 resources.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @returns ResourceSavingsReport Successful Response
     * @throws ApiError
     */
    public static getSavingsByResource(data: ReportsGetSavingsByResourceData = {}): CancelablePromise<ReportsGetSavingsByResourceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/resource-saving',
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Top Potential Savings
     * Get top N recommendations with highest potential savings that are in PENDING status
     * for the current workspace within the specified date range.
     * @param data The data for the request.
     * @param data.limit
     * @param data.startDate Start date in ISO format
     * @param data.endDate End date in ISO format
     * @returns TopSavingsReport Successful Response
     * @throws ApiError
     */
    public static getTopPotentialSavings(data: ReportsGetTopPotentialSavingsData = {}): CancelablePromise<ReportsGetTopPotentialSavingsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/top-potential-savings',
            query: {
                limit: data.limit,
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Savings By Service
     * Get savings data grouped by service type for pie chart visualization.
     * @param data The data for the request.
     * @param data.startDate
     * @param data.endDate
     * @returns ServiceSavingsReport Successful Response
     * @throws ApiError
     */
    public static getSavingsByService(data: ReportsGetSavingsByServiceData = {}): CancelablePromise<ReportsGetSavingsByServiceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/service-savings',
            query: {
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Report By Conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns Report Successful Response
     * @throws ApiError
     */
    public static getReportByConversation(data: ReportsGetReportByConversationData): CancelablePromise<ReportsGetReportByConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ResourcesService {
    /**
     * Read Resources
     * Retrieve resources.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.name
     * @param data.resourceType
     * @param data.status
     * @param data.region
     * @returns ResourcesPublic Successful Response
     * @throws ApiError
     */
    public static readResources(data: ResourcesReadResourcesData = {}): CancelablePromise<ResourcesReadResourcesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/',
            query: {
                skip: data.skip,
                limit: data.limit,
                name: data.name,
                resource_type: data.resourceType,
                status: data.status,
                region: data.region
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Resource
     * Create new resource.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ResourcePublic Successful Response
     * @throws ApiError
     */
    public static createResource(data: ResourcesCreateResourceData): CancelablePromise<ResourcesCreateResourceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/resources/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Resource
     * Get resource by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns ResourceRead Successful Response
     * @throws ApiError
     */
    public static readResource(data: ResourcesReadResourceData): CancelablePromise<ResourcesReadResourceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Resource
     * Update a resource.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns ResourcePublic Successful Response
     * @throws ApiError
     */
    public static updateResource(data: ResourcesUpdateResourceData): CancelablePromise<ResourcesUpdateResourceResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Resource
     * Delete a resource.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteResource(data: ResourcesDeleteResourceData): CancelablePromise<ResourcesDeleteResourceResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/resources/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class SampleDataService {
    /**
     * Create Sample Resources
     * Generate sample resources and metrics for testing.
     *
     * Args:
     * resource_type: Type of resource (EC2, RDS, etc.)
     * resource_count: Number of resources to generate
     * metrics_per_resource: Number of metric points per resource
     * days_back: Number of days to generate metrics for
     * @param data The data for the request.
     * @param data.resourceType
     * @param data.resourceCount
     * @param data.metricsPerResource
     * @param data.daysBack
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createSampleResources(data: SampleDataCreateSampleResourcesData): CancelablePromise<SampleDataCreateSampleResourcesResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/resources/{resource_type}',
            path: {
                resource_type: data.resourceType
            },
            query: {
                resource_count: data.resourceCount,
                metrics_per_resource: data.metricsPerResource,
                days_back: data.daysBack
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Sample Metrics
     * Create sample metrics for specified resource type.
     *
     * Parameters:
     * - resource_type: Type of resource (EC2, RDS, etc.)
     * - num_points: Number of data points to generate per metric
     * - days_back: Number of days to generate data for
     * @param data The data for the request.
     * @param data.resourceType
     * @param data.numPoints
     * @param data.daysBack
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createSampleMetrics(data: SampleDataCreateSampleMetricsData): CancelablePromise<SampleDataCreateSampleMetricsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/metrics/{resource_type}',
            path: {
                resource_type: data.resourceType
            },
            query: {
                num_points: data.numPoints,
                days_back: data.daysBack
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Sample Recommendations
     * @param data The data for the request.
     * @param data.totalRecord
     * @returns RecommendationsPublic Successful Response
     * @throws ApiError
     */
    public static createSampleRecommendations(data: SampleDataCreateSampleRecommendationsData = {}): CancelablePromise<SampleDataCreateSampleRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/recommendations',
            query: {
                total_record: data.totalRecord
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ShareChatService {
    /**
     * Create Share Link
     * Create a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static createShareLink(data: ShareChatCreateShareLinkData): CancelablePromise<ShareChatCreateShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Revoke Share Link
     * Revoke a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static revokeShareLink(data: ShareChatRevokeShareLinkData): CancelablePromise<ShareChatRevokeShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Share Link
     * Get a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static getShareLink(data: ShareChatGetShareLinkData): CancelablePromise<ShareChatGetShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Shared Conversation
     * Get message history for a shared conversation by share ID (no authentication required)
     * @param data The data for the request.
     * @param data.shareId
     * @param data.limit
     * @returns MessageHistoryPublic Successful Response
     * @throws ApiError
     */
    public static getSharedConversation(data: ShareChatGetSharedConversationData): CancelablePromise<ShareChatGetSharedConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/shared/{share_id}',
            path: {
                share_id: data.shareId
            },
            query: {
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class SubscriptionsService {
    /**
     * Get Available Plans
     * Get available plans
     * @returns ProductResponse Successful Response
     * @throws ApiError
     */
    public static getAvailablePlans(): CancelablePromise<SubscriptionsGetAvailablePlansResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/plans'
        });
    }

    /**
     * Get User Subscription Status
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getUserSubscriptionStatus(): CancelablePromise<SubscriptionsGetUserSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/status'
        });
    }

    /**
     * Get Workspace Subscription Status
     * Get subscription status for a workspace
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getWorkspaceSubscriptionStatus(data: SubscriptionsGetWorkspaceSubscriptionStatusData): CancelablePromise<SubscriptionsGetWorkspaceSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/workspace/{workspace_id}/status',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Checkout Session
     * Create a checkout session for subscription
     * @param data The data for the request.
     * @param data.priceId
     * @returns CheckoutSessionResponse Successful Response
     * @throws ApiError
     */
    public static createCheckoutSession(data: SubscriptionsCreateCheckoutSessionData): CancelablePromise<SubscriptionsCreateCheckoutSessionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/checkout',
            query: {
                price_id: data.priceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get User Payment Methods
     * Get current user's payment methods
     * @param data The data for the request.
     * @param data.paymentType
     * @returns PaymentMethodResponse Successful Response
     * @throws ApiError
     */
    public static getUserPaymentMethods(data: SubscriptionsGetUserPaymentMethodsData = {}): CancelablePromise<SubscriptionsGetUserPaymentMethodsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/payment-methods',
            query: {
                payment_type: data.paymentType
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get User Invoices
     * Get current user's invoices
     * @param data The data for the request.
     * @param data.limit
     * @param data.status
     * @returns InvoiceResponse Successful Response
     * @throws ApiError
     */
    public static getUserInvoices(data: SubscriptionsGetUserInvoicesData = {}): CancelablePromise<SubscriptionsGetUserInvoicesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/invoices',
            query: {
                limit: data.limit,
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Submit Enterprise Enquiry
     * Submit an enterprise plan enquiry
     * @param data The data for the request.
     * @param data.requestBody
     * @returns EnterpriseEnquiryMessageResponse Successful Response
     * @throws ApiError
     */
    public static submitEnterpriseEnquiry(data: SubscriptionsSubmitEnterpriseEnquiryData): CancelablePromise<SubscriptionsSubmitEnterpriseEnquiryResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/enterprise-enquiry',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Submit Plan Change Request
     * Submit a plan change request
     * @param data The data for the request.
     * @param data.requestBody
     * @returns PlanChangeRequestResponse Successful Response
     * @throws ApiError
     */
    public static submitPlanChangeRequest(data: SubscriptionsSubmitPlanChangeRequestData): CancelablePromise<SubscriptionsSubmitPlanChangeRequestResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/plan-change',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Webhook
     * Handle webhook events from payment provider
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static webhook(): CancelablePromise<SubscriptionsWebhookResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/webhook'
        });
    }

    /**
     * Cancel Subscription
     * Cancel subscription for the current user
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static cancelSubscription(): CancelablePromise<SubscriptionsCancelSubscriptionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/cancel'
        });
    }

}

export class TasksService {
    /**
     * Create Task
     * Create new task.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static createTask(data: TasksCreateTaskData): CancelablePromise<TasksCreateTaskResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Tasks
     * List tasks with filters.
     * @param data The data for the request.
     * @param data.executionStatus
     * @param data.skip
     * @param data.limit
     * @param data.includeHistory
     * @param data.historyLimit
     * @returns TaskList Successful Response
     * @throws ApiError
     */
    public static listTasks(data: TasksListTasksData = {}): CancelablePromise<TasksListTasksResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/',
            query: {
                execution_status: data.executionStatus,
                skip: data.skip,
                limit: data.limit,
                include_history: data.includeHistory,
                history_limit: data.historyLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task
     * Get task by ID.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.includeHistory
     * @param data.historyLimit
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static getTask(data: TasksGetTaskData): CancelablePromise<TasksGetTaskResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            query: {
                include_history: data.includeHistory,
                history_limit: data.historyLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Task
     * Update task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTask(data: TasksUpdateTaskData): CancelablePromise<TasksUpdateTaskResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Task
     * Delete task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns TaskDeleteResponse Successful Response
     * @throws ApiError
     */
    public static deleteTask(data: TasksDeleteTaskData): CancelablePromise<TasksDeleteTaskResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Task Enable
     * Update task enable.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.enable
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTaskEnable(data: TasksUpdateTaskEnableData): CancelablePromise<TasksUpdateTaskEnableResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/tasks/{task_id}/enable',
            path: {
                task_id: data.taskId
            },
            query: {
                enable: data.enable
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Stop Task Execution
     * Stop an executing task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.conversationId
     * @returns TaskStopResponse Successful Response
     * @throws ApiError
     */
    public static stopTaskExecution(data: TasksStopTaskExecutionData): CancelablePromise<TasksStopTaskExecutionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/{task_id}/stop',
            path: {
                task_id: data.taskId
            },
            query: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Task Progress
     * Get task execution progress by conversation id.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.conversationId
     * @returns TaskExecutionStatus Successful Response
     * @throws ApiError
     */
    public static getTaskProgress(data: TasksGetTaskProgressData): CancelablePromise<TasksGetTaskProgressResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}/progress',
            path: {
                task_id: data.taskId
            },
            query: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Continue Interrupted Task
     * Continue an interrupted autonomous agent task.
     * @param data The data for the request.
     * @param data.taskHistoryId
     * @param data.requestBody
     * @returns TaskContinueResponse Successful Response
     * @throws ApiError
     */
    public static continueInterruptedTask(data: TasksContinueInterruptedTaskData): CancelablePromise<TasksContinueInterruptedTaskResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/task-history/{task_history_id}/continue',
            path: {
                task_history_id: data.taskHistoryId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class TaskTemplatesService {
    /**
     * Generate
     * Generate the task template based on user's input
     * @param data The data for the request.
     * @param data.input
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static generate(data: TaskTemplatesGenerateData): CancelablePromise<TaskTemplatesGenerateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/generate',
            query: {
                input: data.input
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Template
     * Create new task template.
     * @param data The data for the request.
     * @param data.requestBody
     * @param data.isDefault
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static createTemplate(data: TaskTemplatesCreateTemplateData): CancelablePromise<TaskTemplatesCreateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/',
            query: {
                is_default: data.isDefault
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * List Templates
     * List task templates with optional category and service filters.
     * @param data The data for the request.
     * @param data.category
     * @param data.services
     * @param data.includeDefaults
     * @param data.skip
     * @param data.limit
     * @param data.searchQuery
     * @returns TaskTemplateList Successful Response
     * @throws ApiError
     */
    public static listTemplates(data: TaskTemplatesListTemplatesData = {}): CancelablePromise<TaskTemplatesListTemplatesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/',
            query: {
                category: data.category,
                services: data.services,
                include_defaults: data.includeDefaults,
                skip: data.skip,
                limit: data.limit,
                search_query: data.searchQuery
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Get Template
     * Get template by ID.
     * @param data The data for the request.
     * @param data.templateId
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static getTemplate(data: TaskTemplatesGetTemplateData): CancelablePromise<TaskTemplatesGetTemplateResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Template
     * Update template.
     * @param data The data for the request.
     * @param data.templateId
     * @param data.requestBody
     * @returns TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static updateTemplate(data: TaskTemplatesUpdateTemplateData): CancelablePromise<TaskTemplatesUpdateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Template
     * Delete template.
     * @param data The data for the request.
     * @param data.templateId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteTemplate(data: TaskTemplatesDeleteTemplateData): CancelablePromise<TaskTemplatesDeleteTemplateResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class ToolsService {
    /**
     * Script Execution
     * Script execution to the bash environment.
     * @param data The data for the request.
     * @param data.script
     * @returns ScriptExecutionResponse Successful Response
     * @throws ApiError
     */
    public static scriptExecution(data: ToolsScriptExecutionData): CancelablePromise<ToolsScriptExecutionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tools/run',
            query: {
                script: data.script
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class UsersService {
    /**
     * Read Users
     * Retrieve users based on workspace relationship.
     * Only returns users that belong to the current user's active workspace.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns UsersPublic Successful Response
     * @throws ApiError
     */
    public static readUsers(data: UsersReadUsersData = {}): CancelablePromise<UsersReadUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create User
     * Create new user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static createUser(data: UsersCreateUserData): CancelablePromise<UsersCreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read User Me
     * Get current user.
     * @returns UserDetail Successful Response
     * @throws ApiError
     */
    public static readUserMe(): CancelablePromise<UsersReadUserMeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/me'
        });
    }

    /**
     * Delete User Me
     * Delete own user.
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUserMe(): CancelablePromise<UsersDeleteUserMeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/me'
        });
    }

    /**
     * Update User Me
     * Update own user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUserMe(data: UsersUpdateUserMeData): CancelablePromise<UsersUpdateUserMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Password Me
     * Update own password.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static updatePasswordMe(data: UsersUpdatePasswordMeData): CancelablePromise<UsersUpdatePasswordMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me/password',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read User By Id
     * Get a specific user by id.
     * @param data The data for the request.
     * @param data.userId
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static readUserById(data: UsersReadUserByIdData): CancelablePromise<UsersReadUserByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update User
     * Update a user.
     * @param data The data for the request.
     * @param data.userId
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUser(data: UsersUpdateUserData): CancelablePromise<UsersUpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete User
     * Delete a user.
     * @param data The data for the request.
     * @param data.userId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUser(data: UsersDeleteUserData): CancelablePromise<UsersDeleteUserResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Switch Workspace
     * Allow user to get new token for a different workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static switchWorkspace(data: UsersSwitchWorkspaceData): CancelablePromise<UsersSwitchWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/switch-workspace/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class UtilsService {
    /**
     * Test Email
     * Test emails.
     * @param data The data for the request.
     * @param data.emailTo
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static testEmail(data: UtilsTestEmailData): CancelablePromise<UtilsTestEmailResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/test-email/',
            query: {
                email_to: data.emailTo
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Health Check
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static healthCheck(): CancelablePromise<UtilsHealthCheckResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/health-check/'
        });
    }

    /**
     * Publish Message
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static publishMessage(data: UtilsPublishMessageData): CancelablePromise<UtilsPublishMessageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/publish/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Enqueue Message
     * @param data The data for the request.
     * @param data.taskName
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static enqueueMessage(data: UtilsEnqueueMessageData): CancelablePromise<UtilsEnqueueMessageResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/utils/enqueue/',
            query: {
                task_name: data.taskName
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class WorkflowsService {
    /**
     * Read Workflows
     * Retrieve workflows.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns WorkflowsPublic Successful Response
     * @throws ApiError
     */
    public static readWorkflows(data: WorkflowsReadWorkflowsData = {}): CancelablePromise<WorkflowsReadWorkflowsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow
     * Create new workflow.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static createWorkflow(data: WorkflowsCreateWorkflowData): CancelablePromise<WorkflowsCreateWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow
     * Get workflow by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static readWorkflow(data: WorkflowsReadWorkflowData): CancelablePromise<WorkflowsReadWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workflow
     * Update a workflow.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static updateWorkflow(data: WorkflowsUpdateWorkflowData): CancelablePromise<WorkflowsUpdateWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workflow
     * Delete a workflow.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkflow(data: WorkflowsDeleteWorkflowData): CancelablePromise<WorkflowsDeleteWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workflows/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow From Template
     * Create new workflow from YAML template.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns WorkflowPublic Successful Response
     * @throws ApiError
     */
    public static createWorkflowFromTemplate(data: WorkflowsCreateWorkflowFromTemplateData): CancelablePromise<WorkflowsCreateWorkflowFromTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/template',
            query: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workflow Node
     * Create new workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.requestBody
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static createWorkflowNode(data: WorkflowsCreateWorkflowNodeData): CancelablePromise<WorkflowsCreateWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/nodes/',
            path: {
                workflow_id: data.workflowId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow Nodes
     * Retrieve workflow nodes.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.skip
     * @param data.limit
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static readWorkflowNodes(data: WorkflowsReadWorkflowNodesData): CancelablePromise<WorkflowsReadWorkflowNodesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{workflow_id}/nodes/',
            path: {
                workflow_id: data.workflowId
            },
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workflow Node
     * Get workflow node by ID.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static readWorkflowNode(data: WorkflowsReadWorkflowNodeData): CancelablePromise<WorkflowsReadWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workflow Node
     * Update a workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @param data.requestBody
     * @returns WorkflowNodePublic Successful Response
     * @throws ApiError
     */
    public static updateWorkflowNode(data: WorkflowsUpdateWorkflowNodeData): CancelablePromise<WorkflowsUpdateWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workflow Node
     * Delete a workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkflowNode(data: WorkflowsDeleteWorkflowNodeData): CancelablePromise<WorkflowsDeleteWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Run Workflow Node
     * Run a specific workflow node.
     * @param data The data for the request.
     * @param data.workflowId
     * @param data.nodeId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static runWorkflowNode(data: WorkflowsRunWorkflowNodeData): CancelablePromise<WorkflowsRunWorkflowNodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/nodes/{node_id}/run',
            path: {
                workflow_id: data.workflowId,
                node_id: data.nodeId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Run Workflow
     * Run an entire workflow.
     * @param data The data for the request.
     * @param data.workflowId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static runWorkflow(data: WorkflowsRunWorkflowData): CancelablePromise<WorkflowsRunWorkflowResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workflows/{workflow_id}/run',
            path: {
                workflow_id: data.workflowId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}

export class WorkspacesService {
    /**
     * Read Workspaces
     * Retrieve workspaces - both owned and invited (non-deleted only).
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns WorkspacesPublic Successful Response
     * @throws ApiError
     */
    public static readWorkspaces(data: WorkspacesReadWorkspacesData = {}): CancelablePromise<WorkspacesReadWorkspacesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Create Workspace
     * Create new workspace. Only users who already own workspaces or superusers can create new ones.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static createWorkspace(data: WorkspacesCreateWorkspaceData): CancelablePromise<WorkspacesCreateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workspaces/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Read Workspace
     * Get Workspace by ID. Accessible by workspace owner and invited users.
     * @param data The data for the request.
     * @param data.id
     * @returns WorkspaceDetail Successful Response
     * @throws ApiError
     */
    public static readWorkspace(data: WorkspacesReadWorkspaceData): CancelablePromise<WorkspacesReadWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Update Workspace
     * Update a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static updateWorkspace(data: WorkspacesUpdateWorkspaceData): CancelablePromise<WorkspacesUpdateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }

    /**
     * Delete Workspace
     * Delete a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.id
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteWorkspace(data: WorkspacesDeleteWorkspaceData): CancelablePromise<WorkspacesDeleteWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workspaces/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }

}
