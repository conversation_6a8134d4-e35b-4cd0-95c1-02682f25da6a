import { z } from 'zod';

const PathsSchema = z.object({
  auth: z.object({
    signIn: z.string().min(1),
    signUp: z.string().min(1),
    forgotPassword: z.string().min(1),
  }),
  app: z.object({
    home: z.string().min(1),
    slackComplete: z.string().min(1),
    dashboard: z.string().min(1),
    items: z.string().min(1),
    users: z.string().min(1),
    workspaces: z.string().min(1),

    // Resources
    resources: z.string().min(1),
    resourceDetail: z.function().args(z.string()).returns(z.string()),

    // Recommendations
    recommendations: z.string().min(1),
    recommendationDetail: z.function().args(z.string()).returns(z.string()),

    // Tasks
    tasks: z.string().min(1),
    taskDetail: z.function().args(z.string()).returns(z.string()),

    agents: z.string().min(1),
    workflows: z.string().min(1),
    profile: z.string().min(1),
    password: z.string().min(1),
    notFound: z.string().min(1),
    issueBoard: z.string().min(1),
    issues: z.string().min(1),
    autonomous: z.string().min(1),
    subscription: z.string().min(1),
    billing: z.string().min(1),
    notifications: z.string().min(1),
    purchase: z.string().min(1),
    activate: z.string().min(1),
    resendVerification: z.string().min(1),
    knowledgeBase: z.string().min(1),
    alerts: z.string().min(1),
    shared: z.string().min(1),
  }),
});

const pathsConfig = PathsSchema.parse({
  auth: {
    signIn: '/login',
    signUp: '/signup',
    forgotPassword: '/forgot-password',
  },
  app: {
    home: '/',
    slackComplete: '/slack-complete',
    dashboard: '/dashboard',
    items: '/items',
    users: '/users',
    workspaces: '/workspaces',

    // Resources
    resources: '/resources',
    resourceDetail: (id: string) => `/resources/${id}`,

    // Recommendations
    recommendations: '/recommendations',
    recommendationDetail: (id: string) => `/recommendations/${id}`,

    // Tasks
    tasks: '/tasks',
    taskDetail: (id: string) => `/tasks/${id}`,

    agents: '/agents',
    workflows: '/workflows',
    profile: '/profile',
    password: '/password',
    notFound: '/404',
    issueBoard: '/issue-board',
    issues: '/issues',
    autonomous: '/autonomous',
    subscription: '/subscription',
    billing: '/billing',
    notifications: '/notifications',
    purchase: '/purchase',
    activate: '/activate',
    resendVerification: '/resend-verification',
    knowledgeBase: '/kb',
    alerts: '/alerts',
    shared: '/shared',
  },
} satisfies z.infer<typeof PathsSchema>);

export default pathsConfig;
