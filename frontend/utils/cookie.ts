import { isBrowser } from './is-browser';

// Add this new function to get cookies safely
export async function getCookieValue(
  cookieName: string,
): Promise<string | undefined> {
  if (isBrowser()) {
    // Client-side cookie handling
    const value = document.cookie
      .split('; ')
      .find((row) => row.startsWith(`${cookieName}=`))
      ?.split('=')[1];

    return value;
  }

  // Check if we're in a server context
  try {
    const cookieStore = await import('next/headers').then((mod) =>
      mod.cookies(),
    );

    return cookieStore.get(cookieName)?.value;
  } catch {
    // If cookies() fails, we're in an environment where it's not supported
    return undefined;
  }
}

// Add this new function to remove cookies safely
export async function removeCookie(cookieName: string): Promise<void> {
  if (isBrowser()) {
    // Client-side cookie removal
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    return;
  }

  // Server-side cookie removal
  try {
    const cookieStore = await import('next/headers').then((mod) =>
      mod.cookies(),
    );
    cookieStore.delete(cookieName);
    // cookieStore.clear()
  } catch {
    // If cookies() fails, we're in an environment where it's not supported
    return;
  }
}
