type PaginationSearchParams = {
  page?: number;
  limit?: number;
};

export type WithPaginationDefaults<T extends PaginationSearchParams> = T & {
  page: number;
  limit: number;
};

export const withPaginationDefaults = <T extends PaginationSearchParams>(
  params: T,
): WithPaginationDefaults<T> => {
  return {
    ...params,
    page: params.page ?? 1,
    limit: params.limit ?? 10,
  };
};

export type ConvertPageToSkip<
  T extends {
    page: number;
  },
> = Omit<T, 'page'> & { skip: number };

export const handleSkipOfPagination = <
  T extends {
    page: number;
  },
>({
  page,
  ...params
}: T): ConvertPageToSkip<T> => {
  return {
    ...params,
    skip: (page - 1) * 10,
  };
};
