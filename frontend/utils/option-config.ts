import { keyBy, map } from 'lodash';

/**
 * Represents a configuration item with a value and label
 *
 * @template T - Type of the item value (string literal)
 * @template K - Type of additional properties that must include a label
 */
type Item<
  T extends string,
  K extends Record<string, unknown> & {
    label: string;
  },
> = Record<T, K>[T] & {
  value: T;
  label: string;
};

/**
 * Utility class for managing configuration objects with labeled values
 *
 * Provides functionality to transform configuration objects into lists and
 * lookup objects, making them easier to use in UI components like dropdowns.
 *
 * @template T - Type of the configuration key (string literal)
 * @template K - Type of the configuration value (must include a label property)
 */
class UtilityConfig<
  T extends string,
  K extends Record<string, unknown> & {
    label: string;
  },
> {
  /**
   * Array of configuration items with values and labels
   */
  readonly LIST: Item<T, K>[];

  /**
   * Creates a new UtilityConfig instance
   *
   * @param CONFIG - The configuration object to manage
   */
  constructor(public readonly CONFIG: Record<T, K>) {
    this.LIST = map(CONFIG, (value, genericKey) => {
      const key = genericKey as T;

      return {
        ...value,
        value: key,
        label: value.label,
      };
    });
  }

  /**
   * Gets an object with configuration items keyed by their values
   *
   * @returns Record of configuration items indexed by their values
   */
  get OBJECT() {
    return keyBy(this.LIST, 'value') as Record<T, Item<T, K>>;
  }

  /**
   * Gets the label for a given configuration value
   *
   * @param value - The configuration value to get the label for
   * @returns The label associated with the value
   */
  getLabel = (value: T) => {
    return this.CONFIG[value].label;
  };
}

/**
 * Creates a new utility configuration manager
 *
 * Factory function that instantiates a UtilityConfig with the provided config object.
 * Used for creating type-safe configuration utilities for dropdown options, status mappings, etc.
 *
 * @template T - Type of the configuration key (string literal)
 * @template K - Type of the configuration value (must include a label property)
 * @param config - The configuration object to manage
 * @returns A new UtilityConfig instance
 */
export const createUtilityConfig = <
  T extends string,
  K extends Record<string, unknown> & {
    label: string;
  },
>(
  config: Record<T, K>,
) => {
  return new UtilityConfig<T, K>(config);
};
