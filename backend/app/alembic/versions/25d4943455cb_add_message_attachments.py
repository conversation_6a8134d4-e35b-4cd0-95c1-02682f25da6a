"""add message_attachments

Revision ID: 25d4943455cb
Revises: d9bfb00f4b1f
Create Date: 2025-07-04 23:28:36.357513

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '25d4943455cb'
down_revision = 'd9bfb00f4b1f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_attachments',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('message_id', sa.Uuid(), nullable=True),
    sa.Column('owner_id', sa.Uuid(), nullable=False),
    sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('original_filename', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('file_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('storage_key', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('thumbnail_key', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['message_id'], ['message.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_message_attachments_message_id'), 'message_attachments', ['message_id'], unique=False)
    op.create_index(op.f('ix_message_attachments_owner_id'), 'message_attachments', ['owner_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_message_attachments_owner_id'), table_name='message_attachments')
    op.drop_index(op.f('ix_message_attachments_message_id'), table_name='message_attachments')
    op.drop_table('message_attachments')
    # ### end Alembic commands ###
