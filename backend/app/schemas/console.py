from enum import Enum

from pydantic import BaseModel, Field


class FileType(str, Enum):
    FILE = "file"
    DIRECTORY = "directory"


class FileNode(BaseModel):
    """Simplified file node matching frontend interface"""

    id: str = Field(..., description="Unique identifier for the file/directory")
    name: str = Field(..., description="File or directory name")
    path: str = Field(..., description="Full path")
    type: FileType = Field(..., description="File type (file or directory)")
    children: list["FileNode"] | None = Field(
        None, description="Child nodes for directories"
    )
    content: str | None = Field(None, description="File content for files")


class FileListResponse(BaseModel):
    """Response for directory listing"""

    files: list[FileNode] = Field(..., description="List of files and directories")
    current_path: str = Field(..., description="Current directory path")


class FileContentResponse(BaseModel):
    """Response for file content requests"""

    content: str = Field(..., description="File content")
    path: str = Field(..., description="File path")
    name: str = Field(..., description="File name")
