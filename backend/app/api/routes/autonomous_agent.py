import json
import logging
import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from pydantic import UUID4

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    ConversationCreateRequest,
    ConversationPublic,
    ConversationsPublic,
    MessageHistoryPublic,
    MessagePublic,
    StreamResponse,
)
from app.repositories.conversation import ConversationRepository
from app.services.agent import AutonomousAgentService
from app.utils import generate_conversation_title

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/conversations", response_model=ConversationPublic)
async def create_conversation(
    current_user: CurrentUser,
    *,
    session: SessionDep,
    conversation_in: ConversationCreateRequest,
) -> ConversationPublic:
    """Create a new conversation."""
    conversation_repo = ConversationRepository(session=session)

    try:
        conversation = conversation_repo.create_conversation(
            agent_id=conversation_in.agent_id,
            model_provider=conversation_in.model_provider,
            instructions=conversation_in.instructions,
            resource_id=conversation_in.resource_id,
        )
        return ConversationPublic.model_validate(conversation)
    except ValueError as e:
        logger.error(f"Error creating conversation: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"An unexpected error occurred while creating a conversation: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating the conversation.",
        )


@router.get("/conversations", response_model=ConversationsPublic)
def get_conversations(
    current_user: CurrentUser,
    session: SessionDep,
    agent_id: UUID4 | None = None,
    resource_id: UUID4 | None = None,
    model_provider: str | None = "bedrock",
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination"
    ),
    limit: int = Query(
        default=10, ge=1, le=100, description="Maximum number of records to return"
    ),
) -> ConversationsPublic:
    """Get list of conversations with filtering and pagination.

    Args:
        current_user: The authenticated user
        session: Database session
        agent_id: Optional agent ID to filter by
        resource_id: Optional resource ID to filter by
        model_provider: Model provider to use (defaults to 'bedrock')
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return (1-100)

    Returns:
        ConversationsPublic: Paginated list of conversations

    Raises:
        HTTPException: If conversation not found or other error occurs
    """
    conversation_repo = ConversationRepository(session=session)

    try:
        conversations, total_count = conversation_repo.get_conversations(
            agent_id=agent_id,
            resource_id=resource_id,
            skip=skip,
            limit=limit,
        )

        if (
            not conversations and skip == 0
        ):  # Only create new conversation if we're on first page
            conversation = conversation_repo.create_conversation(
                agent_id=agent_id,
                model_provider=model_provider,
                resource_id=resource_id,
            )
            conversations = [conversation]
            total_count = 1

        return ConversationsPublic(
            data=[ConversationPublic.model_validate(conv) for conv in conversations],
            count=total_count,
        )
    except ValueError as e:
        logger.error(f"Error in get_conversations: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"An unexpected error occurred while getting conversations: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while getting conversations.",
        )


@router.get("/messages/{conversation_id}", response_model=MessageHistoryPublic)
async def get_messages_history(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    limit: int = 10,
) -> MessageHistoryPublic:
    """Get message history for a conversation."""
    try:
        autonomous_agent_service = AutonomousAgentService(session)
        return autonomous_agent_service.get_conversation_history(conversation_id, limit)
    except Exception as e:
        logger.error(
            f"Error getting message history for conversation {conversation_id}: {e}"
        )
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while getting the message history.",
        )


@router.post("/chat/{conversation_id}/stream", response_model=StreamResponse)
async def chat_stream(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    message: MessagePublic,
    session: SessionDep,
) -> StreamingResponse:
    """Stream chat responses from the agent.

    Args:
        conversation_id: ID of the conversation
        message: User message with content and resume flag
        session: Database session

    Returns:
        StreamingResponse: Server-sent events stream of agent responses

    Raises:
        HTTPException: If conversation not found (404) or no previous message found (404)
    """
    conversation_repo = ConversationRepository(session=session)
    conversation = conversation_repo.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create service
    autonomous_agent_service = AutonomousAgentService(session)

    async def event_generator():
        need_title_generation = False

        try:
            # Use enhanced process_message_stream that handles DB operations internally
            async for response in autonomous_agent_service.process_message_stream(
                conversation_id=conversation_id,
                message_content=message.content,
                message_action_type=message.action_type,
                resume=message.resume,
                approve=message.approve,
                restore=message.restore,
                restore_message_id=message.message_id,
                user_id=current_user.id,
                resource_id=conversation.resource_id,
                attachment_ids=message.attachment_ids,
            ):
                if response["type"] == "error":
                    # Convert error responses to proper HTTP exceptions
                    error_content = response["content"]
                    if "not found" in error_content.lower():
                        raise HTTPException(status_code=404, detail=error_content)
                    elif "credentials" in error_content.lower():
                        raise HTTPException(status_code=403, detail=error_content)
                    else:
                        raise HTTPException(status_code=500, detail=error_content)
                elif response["type"] == "needs_title":
                    # Handle title generation signal from service
                    need_title_generation = True
                    continue
                else:
                    # Send normal responses to client
                    yield f"data: {json.dumps(response)}\n\n"

            yield f"data: {json.dumps({'type': 'complete'})}\n\n"

            # Trigger memory extraction in the background
            autonomous_agent_service.trigger_memory_extraction(conversation_id)

            # Generate a title after the assistant response if needed
            if need_title_generation:
                new_title = await generate_conversation_title(session, conversation_id)
                conversation_repo.rename_conversation(conversation_id, new_title)

        except HTTPException as ex:
            # Re-raise HTTP exceptions as they already have the correct status code
            error_message = {
                "type": "error",
                "content": ex.detail,
                "status": ex.status_code,
            }
            logger.error(f"HTTP error in stream: {ex.status_code} - {ex.detail}")
            yield f"data: {json.dumps(error_message)}\n\n"
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            return
        except Exception as e:
            # Catch all other exceptions and return a 500 error
            error_message = {"type": "error", "content": str(e), "status": 500}
            logger.exception(f"Unexpected error in autonomous agent stream: {str(e)}")
            yield f"data: {json.dumps(error_message)}\n\n"
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            return

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@router.put("/conversations/{conversation_id}/name")
def rename_conversation(
    conversation_id: uuid.UUID, session: SessionDep, name: str
) -> Any:
    try:
        conversation_repo = ConversationRepository(session=session)
        conversation_repo.rename_conversation(conversation_id, name)
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error renaming conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while renaming the conversation.",
        )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: uuid.UUID,
    session: SessionDep,
) -> Any:
    """Delete a conversation and its associated LangGraph thread data."""
    try:
        conversation_repo = ConversationRepository(session=session)
        conversation_repo.delete_conversation(conversation_id)
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while deleting the conversation.",
        )
