"""
PTY WebSocket Backend using FastAPI with secure sandbox
Provides a WebSocket server that creates a pseudo-terminal in a sandboxed environment
and forwards data between the terminal and WebSocket clients.
"""

import asyncio
import fcntl
import json
import logging
import os
import pty
import signal
import struct
import subprocess
import termios

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, status

from app.api.routes.tools import WorkspaceEnvironment, WorkspaceManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/console", tags=["console"])


class SecurePTYHandler:
    def __init__(self, workspace_id: str):
        self.master_fd: int | None = None
        self.slave_fd: int | None = None
        self.process: subprocess.Popen | None = None
        self.websocket: WebSocket | None = None
        self.running: bool = False
        self.workspace_id = workspace_id
        self.workspace_path = None
        self.aws_access_key_id: str | None = None
        self.aws_secret_access_key: str | None = None
        self.aws_default_region: str | None = "us-east-1"

    def set_aws_credentials(
        self,
        aws_access_key_id: str | None = None,
        aws_secret_access_key: str | None = None,
        aws_default_region: str | None = None,
    ):
        """Set AWS credentials before PTY creation"""
        self.aws_access_key_id = aws_access_key_id
        self.aws_secret_access_key = aws_secret_access_key
        self.aws_default_region = aws_default_region or "us-east-1"

        logger.info(f"AWS credentials set for workspace {self.workspace_id}")

    async def create_pty(self) -> bool:
        """Create a new pseudo-terminal in a sandboxed environment with AWS credentials"""
        try:
            # Setup workspace
            self.workspace_path = WorkspaceManager.setup_workspace(self.workspace_id)

            # Create master and slave file descriptors
            self.master_fd, self.slave_fd = pty.openpty()

            # Set terminal attributes for proper job control and echoing
            attrs = termios.tcgetattr(self.slave_fd)
            # Input flags
            attrs[0] = attrs[0] | termios.BRKINT | termios.ICRNL | termios.IMAXBEL
            # Output flags
            attrs[1] = attrs[1] | termios.OPOST | termios.ONLCR
            # Control flags
            attrs[2] = attrs[2] | termios.CS8 | termios.CREAD
            # Local flags - enable all echo and job control
            attrs[3] = attrs[3] | (
                termios.ISIG
                | termios.ICANON
                | termios.IEXTEN
                | termios.ECHO
                | termios.ECHOE
                | termios.ECHOK
                | termios.ECHOCTL
                | termios.ECHOKE
            )
            termios.tcsetattr(self.slave_fd, termios.TCSANOW, attrs)

            # Create clean environment with terminal settings and AWS credentials
            env = WorkspaceEnvironment.create_clean_environment(
                self.workspace_path,
                self.aws_access_key_id,
                self.aws_secret_access_key,
                self.aws_default_region,
            )
            env.update(
                {
                    "TERM": "xterm-256color",
                    "COLORTERM": "truecolor",
                    "LANG": "C.UTF-8",  # Use C.UTF-8 instead of en_US.UTF-8
                    "LC_ALL": "C.UTF-8",
                    "SHELL": "/bin/bash",
                    "PS1": "$ ",  # Simple consistent prompt
                    "USER": "user",  # Set as regular user
                    "HOME": "/workdir",  # Set home to workspace
                }
            )

            # Build bwrap command normally but use exec to disguise as system process
            bwrap_cmd = [
                "bwrap",
                "--unshare-all",
                "--share-net",  # Enable network access
                "--die-with-parent",
                # Environment variables
                "--setenv",
                "TERM",
                "xterm-256color",
                "--setenv",
                "COLORTERM",
                "truecolor",
                "--setenv",
                "LANG",
                "C.UTF-8",
                "--setenv",
                "LC_ALL",
                "C.UTF-8",
                "--setenv",
                "SHELL",
                "/bin/bash",
                "--setenv",
                "PS1",
                "$ ",
                "--setenv",
                "USER",
                "user",
                "--setenv",
                "HOME",
                "/workdir",
                # Filesystem and mounts
                "--proc",
                "/proc",
                "--tmpfs",
                "/tmp",
                "--dev",
                "/dev",
                "--ro-bind",
                "/usr",
                "/usr",
                "--ro-bind",
                "/bin",
                "/bin",
                "--ro-bind",
                "/lib",
                "/lib",
                "--ro-bind",
                "/sbin",
                "/sbin",
                "--ro-bind",
                "/usr/local",
                "/usr/local",
                "--ro-bind",
                "/etc/resolv.conf",
                "/etc/resolv.conf",
                "--ro-bind",
                "/etc/hosts",
                "/etc/hosts",
                "--bind",
                str(self.workspace_path),
                "/workdir",
                "--chdir",
                "/workdir",
            ]

            if os.path.exists("/lib64"):
                bwrap_cmd.extend(["--ro-bind", "/lib64", "/lib64"])

            for env_var in env:
                bwrap_cmd.extend(["--setenv", env_var, env[env_var]])

            # Start bash directly - environment is already set via --setenv flags
            bwrap_cmd.extend(["bash", "--norc", "--noprofile", "-i"])

            # Define preexec function for proper PTY setup
            def preexec_fn():
                os.setsid()  # Create new session
                try:
                    # Make this the controlling terminal
                    fcntl.ioctl(self.slave_fd, termios.TIOCSCTTY, 0)
                except OSError:
                    pass  # May fail in some environments, that's ok

            # Start shell process in sandbox with proper PTY handling
            self.process = subprocess.Popen(
                bwrap_cmd,
                stdin=self.slave_fd,
                stdout=self.slave_fd,
                stderr=self.slave_fd,
                preexec_fn=preexec_fn,
                restore_signals=True,
            )

            # Close slave fd in parent process
            os.close(self.slave_fd)

            # Make master fd non-blocking
            flags = fcntl.fcntl(self.master_fd, fcntl.F_GETFL)
            fcntl.fcntl(self.master_fd, fcntl.F_SETFL, flags | os.O_NONBLOCK)

            # Set initial terminal size
            self.set_terminal_size(24, 80)

            return True

        except Exception as e:
            logger.error(f"Failed to create secure PTY: {e}")
            await self.cleanup()
            return False

    def set_terminal_size(self, rows: int, cols: int):
        """Set terminal size"""
        try:
            fcntl.ioctl(
                self.master_fd,
                termios.TIOCSWINSZ,
                struct.pack("HHHH", rows, cols, 0, 0),
            )
        except Exception as e:
            logger.error(f"Failed to set terminal size: {e}")

    def is_websocket_connected(self) -> bool:
        """Check if websocket connection is valid"""
        return (
            self.websocket is not None and not self.websocket.client_state.DISCONNECTED
        )

    async def read_from_pty(self):
        """Read data from PTY and send to WebSocket"""
        self.running = True
        buffer = bytearray()

        try:
            while self.running and self.master_fd is not None:
                try:
                    # Read from master fd (non-blocking)
                    chunk = os.read(self.master_fd, 1024)
                    if chunk:
                        buffer.extend(chunk)
                        # Improved UTF-8 decoding to handle partial sequences properly
                        try:
                            # Try to decode the entire buffer
                            text = buffer.decode("utf-8")
                            if self.websocket:
                                # Send raw output to preserve interactive terminal behavior
                                await self.send_to_websocket(text)
                            buffer.clear()
                        except UnicodeDecodeError as e:
                            # Handle incomplete UTF-8 sequences at buffer end
                            if e.start > 0:
                                # We have some valid UTF-8 at the beginning
                                valid_part = buffer[: e.start]
                                remaining_part = buffer[e.start :]

                                try:
                                    text = valid_part.decode("utf-8")
                                    if self.websocket:
                                        await self.send_to_websocket(text)
                                    # Keep the remaining bytes for next iteration
                                    buffer = remaining_part
                                except UnicodeDecodeError:
                                    # If even the valid part fails, clear buffer to avoid infinite loop
                                    buffer.clear()
                            else:
                                # Invalid UTF-8 at the start, keep only last few bytes in case it's a partial sequence
                                if len(buffer) > 4:  # UTF-8 sequences are max 4 bytes
                                    # Keep last 3 bytes in case they form a partial sequence
                                    buffer = buffer[-3:]
                                # If buffer is small, wait for more data
                    else:
                        # No data available, check process
                        if self.process.poll() is not None:
                            logger.info("Shell process terminated")
                            break
                        await asyncio.sleep(0.01)

                except BlockingIOError:
                    # No data available, check process
                    if self.process.poll() is not None:
                        logger.info("Shell process terminated")
                        break
                    await asyncio.sleep(0.01)
                except OSError as e:
                    if e.errno == 5:  # Input/output error
                        if self.process.poll() is not None:
                            logger.info("Shell process terminated")
                            break
                    else:
                        logger.error(f"OSError reading from PTY: {e}")
                        break

        except Exception as e:
            logger.error(f"Error reading from PTY: {e}")
        finally:
            self.running = False
            await self.cleanup()

    async def send_to_websocket(self, data: str):
        """Send data to WebSocket client"""
        try:
            if self.websocket:
                message = json.dumps({"type": "output", "data": data})
                await self.websocket.send_text(message)
        except WebSocketDisconnect:
            logger.info("WebSocket connection closed normally")
            self.running = False
        except Exception as e:
            logger.error(f"Error sending to WebSocket: {e}")
            self.running = False

    def write_to_pty(self, data: str):
        """Write data to PTY"""
        try:
            if self.master_fd and self.process and self.process.poll() is None:
                # Write data directly to PTY - let the shell handle character processing
                os.write(self.master_fd, data.encode("utf-8"))
        except Exception as e:
            logger.error(f"Error writing to PTY: {e}")

    async def cleanup(self):
        """Clean up PTY and process, but keep workspace data persistent"""
        self.running = False
        try:
            if self.process:
                # Send SIGTERM to process group
                try:
                    os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                except ProcessLookupError:
                    pass

                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if still running
                    try:
                        os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                    except ProcessLookupError:
                        pass
                    self.process.wait()

            if self.master_fd:
                os.close(self.master_fd)
                self.master_fd = None

            if self.is_websocket_connected():
                await self.websocket.close(code=1000, reason="Terminal session ended")

            # Keep workspace data persistent - do not cleanup
            # This allows users to maintain their work across terminal sessions

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


@router.websocket("/ws/{workspace_id}")
async def websocket_endpoint(websocket: WebSocket, workspace_id: str):
    """Handle WebSocket connections with workspace isolation - wait for config first"""
    await websocket.accept()
    logger.info(f"New WebSocket connection for workspace: {workspace_id}")

    pty_handler = SecurePTYHandler(workspace_id)
    pty_handler.websocket = websocket
    pty_created = False

    try:
        # Wait for initial config message before creating PTY
        logger.info(f"Waiting for config message for workspace: {workspace_id}")

        # Handle incoming messages
        while True:
            try:
                message = await websocket.receive_text()
                data = json.loads(message)

                if data["type"] == "config" and not pty_created:
                    # Handle initial configuration message (e.g., AWS credentials)
                    if "aws_credentials" in data:
                        aws_creds = data["aws_credentials"]
                        pty_handler.set_aws_credentials(
                            aws_access_key_id=aws_creds.get("aws_access_key_id"),
                            aws_secret_access_key=aws_creds.get(
                                "aws_secret_access_key"
                            ),
                            aws_default_region=aws_creds.get(
                                "aws_default_region", "us-east-1"
                            ),
                        )
                        logger.info(
                            f"Received AWS credentials config for workspace {workspace_id}"
                        )

                    # Now create PTY with credentials
                    if not await pty_handler.create_pty():
                        await websocket.close(code=1011, reason="Failed to create PTY")
                        return

                    pty_created = True

                    # Start reading from PTY
                    read_task = asyncio.create_task(pty_handler.read_from_pty())
                    logger.info(f"PTY created and ready for workspace: {workspace_id}")

                elif pty_created and data["type"] == "input":
                    # Write input to PTY (only if PTY is created)
                    pty_handler.write_to_pty(data["data"])

                elif pty_created and data["type"] == "resize":
                    # Handle terminal resize (only if PTY is created)
                    if pty_handler.master_fd:
                        cols = data.get("cols", 80)
                        rows = data.get("rows", 24)
                        logger.info(f"Resizing terminal to {cols}x{rows}")
                        pty_handler.set_terminal_size(rows, cols)

                elif not pty_created:
                    # If PTY not created yet, ignore non-config messages
                    logger.warning(
                        f"Ignoring {data['type']} message - PTY not created yet for workspace {workspace_id}"
                    )

            except json.JSONDecodeError:
                logger.error("Invalid JSON received")
            except WebSocketDisconnect:
                logger.info("WebSocket connection closed by client")
                break
            except Exception as e:
                logger.error(f"Error handling message: {e}")
                break

    except Exception as e:
        logger.error(f"Error in websocket handler: {e}")
    finally:
        # Cancel the read task if it exists
        if pty_created and "read_task" in locals() and not read_task.done():
            read_task.cancel()
            try:
                await read_task
            except asyncio.CancelledError:
                pass

        await pty_handler.cleanup()


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "ok", "message": "Console WebSocket Server is running"}


@router.get("/files/{workspace_id}")
async def get_files(workspace_id: str):
    """Get files for the specified workspace"""
    try:
        # Setup workspace
        workspace_path = WorkspaceManager.setup_workspace(workspace_id)
        if not workspace_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No workspace found"
            )

        def create_file_node(name: str, path: str, is_dir: bool) -> dict:
            """Helper to create a file node matching the schema"""
            return {
                "id": path,  # Use path as unique identifier
                "name": name,
                "path": path,
                "type": "directory" if is_dir else "file",
                "children": [] if is_dir else None,
                "content": None,  # Content is only provided in file content endpoint
            }

        files = []
        for root, dirs, filenames in os.walk(workspace_path):
            # Skip hidden directories and files
            dirs[:] = [d for d in dirs if not d.startswith(".")]
            filenames = [f for f in filenames if not f.startswith(".")]

            for filename in filenames:
                full_path = os.path.join(root, filename)
                rel_path = os.path.relpath(full_path, workspace_path)
                files.append(create_file_node(filename, rel_path, False))

            # Add directories
            for dirname in dirs:
                full_path = os.path.join(root, dirname)
                rel_path = os.path.relpath(full_path, workspace_path)
                files.append(create_file_node(dirname, rel_path, True))

        return {
            "files": files,
            "current_path": "/",  # Root of workspace
        }
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing files: {str(e)}",
        )


@router.get("/files/{workspace_id}/content")
async def get_file_content(workspace_id: str, path: str):
    """Get file content for the specified workspace"""
    try:
        # Setup workspace
        workspace_path = WorkspaceManager.setup_workspace(workspace_id)
        if not workspace_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No workspace found"
            )

        # Ensure the path is within the workspace
        full_path = os.path.normpath(os.path.join(workspace_path, path))
        if not full_path.startswith(str(workspace_path)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid path - attempting to access file outside workspace",
            )

        # Check if file exists and is a regular file
        if not os.path.isfile(full_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail=f"File not found: {path}"
            )

        # Read file content
        try:
            with open(full_path) as f:
                content = f.read()
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File is not a text file",
            )
        except PermissionError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied to read file",
            )

        return {"content": content, "path": path, "name": os.path.basename(path)}
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error reading file content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading file content: {str(e)}",
        )
